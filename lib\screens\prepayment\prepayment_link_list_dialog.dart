import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/prepayment_product_link.dart';
import '../../models/prepayment_virtual_product.dart';
import '../../models/product.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/prepayment_product_link_provider.dart';
import '../../utils/toast_utils.dart';

/// 링크 목록 다이얼로그: 정렬(선입금 이름순 / 상품 이름순) + 다중 선택 일괄 해제 전용
Future<void> showPrepaymentLinkListDialog({
  required BuildContext context,
  required WidgetRef ref,
  required List<PrepaymentProductLink> links,
}) async {
  if (!context.mounted) return;
  final vps = ref.read(prepaymentVirtualProductNotifierProvider).virtualProducts;
  final products = ref.read(productNotifierProvider).products;
  final state = _LinkListDialogStateHolder();

  await showDialog(
    context: context,
    builder: (dialogCtx) {
      return StatefulBuilder(
        builder: (ctx, setState) {
          // 정렬된 리스트 구성
            List<PrepaymentProductLink> visible = List.of(links);
            int sortKey = state.sortKey; // 0: 선입금 이름, 1: 상품 이름
            visible.sort((a, b) {
              final vpA = vps.firstWhere((e) => e.id == a.virtualProductId, orElse: () => PrepaymentVirtualProduct(id: -1, name: '삭제됨', price: 0, quantity: 0, createdAt: DateTime.fromMillisecondsSinceEpoch(0), eventId: a.eventId));
              final vpB = vps.firstWhere((e) => e.id == b.virtualProductId, orElse: () => PrepaymentVirtualProduct(id: -1, name: '삭제됨', price: 0, quantity: 0, createdAt: DateTime.fromMillisecondsSinceEpoch(0), eventId: b.eventId));
              final pA = products.firstWhere((e) => e.id == a.productId, orElse: () => Product(id: -1, name: '삭제됨', price: 0, quantity: 0, eventId: a.eventId, categoryId: 0));
              final pB = products.firstWhere((e) => e.id == b.productId, orElse: () => Product(id: -1, name: '삭제됨', price: 0, quantity: 0, eventId: b.eventId, categoryId: 0));
              final catA = ref.read(categoryByIdProvider(pA.categoryId));
              final catB = ref.read(categoryByIdProvider(pB.categoryId));
              final prepaidNameA = vpA.name.toLowerCase();
              final prepaidNameB = vpB.name.toLowerCase();
              final productLabelA = ((catA != null ? '${catA.name} - ' : '') + pA.name).toLowerCase();
              final productLabelB = ((catB != null ? '${catB.name} - ' : '') + pB.name).toLowerCase();
              if (sortKey == 0) {
                return prepaidNameA.compareTo(prepaidNameB);
              } else {
                return productLabelA.compareTo(productLabelB);
              }
            });

          return AlertDialog(
            title: Row(
              children: [
                const Icon(Icons.link, size: 20),
                const SizedBox(width: 8),
                Text('링크 목록 (${visible.length})'),
                const Spacer(),
                // 정렬 선택
                DropdownButton<int>(
                  value: state.sortKey,
                  underline: const SizedBox.shrink(),
                  items: const [
                    DropdownMenuItem(value: 0, child: Text('선입금 이름순')),
                    DropdownMenuItem(value: 1, child: Text('상품 이름순')),
                  ],
                  onChanged: (v) { if (v != null) setState(() => state.sortKey = v); },
                ),
                const SizedBox(width: 8),
                // 전체 선택 / 해제 토글
                TextButton(
                  onPressed: visible.isEmpty ? null : () {
                    setState(() {
                      if (state.selectedKeys.length == visible.length) {
                        state.selectedKeys.clear();
                      } else {
                        state.selectedKeys
                          ..clear()
                          ..addAll(visible.map((l) => state.keyOf(l)));
                      }
                    });
                  },
                  child: Text(
                    state.selectedKeys.length == visible.length && visible.isNotEmpty
                      ? '전체 해제'
                      : '전체 선택',
                  ),
                ),
              ],
            ),
            content: SizedBox(
              width: MediaQuery.of(dialogCtx).size.width * 0.6,
              height: MediaQuery.of(dialogCtx).size.height * 0.6,
              child: visible.isEmpty
                  ? const Center(child: Text('연동된 항목이 없습니다'))
                  : ListView.separated(
                      itemCount: visible.length,
                      separatorBuilder: (_, __) => const Divider(height: 1),
                      itemBuilder: (context, index) {
                        final link = visible[index];
                        final vp = vps.firstWhere(
                          (e) => e.id == link.virtualProductId,
                          orElse: () => PrepaymentVirtualProduct(
                            id: -1,
                            name: '삭제됨',
                            price: 0,
                            quantity: 0,
                            createdAt: DateTime.fromMillisecondsSinceEpoch(0),
                            eventId: link.eventId,
                          ),
                        );
                        final product = products.firstWhere(
                          (e) => e.id == link.productId,
                          orElse: () => Product(
                            id: -1,
                            name: '삭제됨',
                            price: 0,
                            quantity: 0,
                            eventId: link.eventId,
                            categoryId: 0,
                          ),
                        );
                        final category = ref.read(categoryByIdProvider(product.categoryId));
                        final productLabel = category != null ? '${category.name} - ${product.name}' : product.name;

                        final key = state.keyOf(link);
                        final selected = state.selectedKeys.contains(key);
                        return InkWell(
                          onTap: () {
                            setState(() {
                              if (selected) {
                                state.selectedKeys.remove(key);
                              } else {
                                state.selectedKeys.add(key);
                              }
                            });
                          },
                          child: Container(
                            color: selected ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.08) : null,
                            child: ListTile(
                              dense: true,
                              leading: Checkbox(
                                value: selected,
                                onChanged: (_) {
                                  setState(() {
                                    if (selected) {
                                      state.selectedKeys.remove(key);
                                    } else {
                                      state.selectedKeys.add(key);
                                    }
                                  });
                                },
                              ),
                              title: Text('${vp.name} ↔ $productLabel'),
                              subtitle: Text('선입금ID:${link.virtualProductId} / 상품ID:${link.productId}'),
                            ),
                          ),
                        );
                      },
                    ),
            ),
            actions: [
              if (state.selectedKeys.isNotEmpty)
                TextButton.icon(
                  icon: const Icon(Icons.link_off, color: Colors.redAccent),
                  label: Text('선택 해제 (${state.selectedKeys.length})', style: const TextStyle(color: Colors.redAccent)),
                  onPressed: () async {
                    final toRemove = <PrepaymentProductLink>[];
                    for (final l in List<PrepaymentProductLink>.from(links)) {
                      if (state.selectedKeys.contains(state.keyOf(l))) {
                        toRemove.add(l);
                      }
                    }
                    if (toRemove.isEmpty) return;
                    await ref.read(prepaymentProductLinkNotifierProvider.notifier).batchRemoveLinks(toRemove);
                    setState(() {
                      links.removeWhere((l) => state.selectedKeys.contains(state.keyOf(l)));
                      state.selectedKeys.clear();
                    });
                    if (context.mounted) ToastUtils.showInfo(context, '${toRemove.length}개 링크 해제');
                  },
                ),
              TextButton(
                onPressed: () => Navigator.of(dialogCtx).pop(),
                child: const Text('닫기'),
              ),
            ],
          );
        },
      );
    },
  );
}

class _LinkListDialogStateHolder {
  int sortKey = 0; // 0: 선입금 이름순, 1: 상품 이름순
  final Set<String> selectedKeys = <String>{};
  String keyOf(PrepaymentProductLink l) => '${l.virtualProductId}_${l.productId}';
}
