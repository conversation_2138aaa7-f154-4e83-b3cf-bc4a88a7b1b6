import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';


import 'package:flutter/material.dart';
import '../models/checklist_template.dart';
import '../models/checklist_item.dart';
import '../models/event_workspace.dart';
import '../repositories/checklist_repository.dart';
import '../utils/logger_utils.dart';
import '../services/database_service.dart';
import 'unified_workspace_provider.dart';

import '../services/realtime_sync_service_main.dart';
import 'data_sync_provider.dart';
import 'realtime_sync_provider.dart';

/// 체크리스트 상태를 관리하는 State 클래스
class ChecklistState {
  final List<ChecklistTemplate> templates;
  final List<ChecklistItem> items;
  final bool isLoading;
  final String? error;
  final bool isUpdating;
  final bool isSaving;
  final Map<int, bool> pendingChanges; // templateId -> isChecked

  const ChecklistState({
    this.templates = const [],
    this.items = const [],
    this.isLoading = false,
    this.error,
    this.isUpdating = false,
    this.isSaving = false,
    this.pendingChanges = const {},
  });

  ChecklistState copyWith({
    List<ChecklistTemplate>? templates,
    List<ChecklistItem>? items,
    bool? isLoading,
    String? error,
    bool? isUpdating,
    bool? isSaving,
    Map<int, bool>? pendingChanges,
  }) {
    return ChecklistState(
      templates: templates ?? this.templates,
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isUpdating: isUpdating ?? this.isUpdating,
      isSaving: isSaving ?? this.isSaving,
      pendingChanges: pendingChanges ?? this.pendingChanges,
    );
  }

  /// 변경사항이 있는지 확인
  bool get hasChanges => pendingChanges.isNotEmpty;

  /// 특정 템플릿의 현재 체크 상태 (변경사항 포함)
  bool getEffectiveCheckState(int templateId, int eventId) {
    // 변경사항이 있으면 변경사항 우선
    if (pendingChanges.containsKey(templateId)) {
      return pendingChanges[templateId]!;
    }

    // 변경사항이 없으면 기존 상태
    return items.any((item) =>
        item.templateId == templateId &&
        item.eventId == eventId &&
        item.isChecked);
  }

  static ChecklistState initialState() => const ChecklistState();
}

/// 체크리스트 상태를 관리하는 Provider
class ChecklistNotifier extends StateNotifier<ChecklistState> {
  static const String _tag = 'ChecklistNotifier';

  final ChecklistRepository repository;
  final Ref ref;
  StreamSubscription<RealtimeDataChange>? _userDataSubscription;
  StreamSubscription<EventRealtimeData>? _eventDataSubscription;
  bool _isInitialized = false;

  ChecklistNotifier(this.repository, this.ref) : super(ChecklistState.initialState()) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupRealtimeSync();
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace != null) {
        _isInitialized = true;
        loadData(showLoading: false);
        _watchCurrentEvent(); // 초기 로드 후에 workspace 변경 감지 시작
      }
    });
  }

  @override
  void dispose() {
    _userDataSubscription?.cancel();
    _eventDataSubscription?.cancel();
    super.dispose();
  }

  /// 현재 행사 변경 감지
  void _watchCurrentEvent() {
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (_isInitialized && next != null && previous?.id != next.id) {
        LoggerUtils.logInfo('행사 변경 감지 - 체크리스트 데이터 로드: ${next.name}', tag: _tag);
        // 실시간 동기화 재설정
        _setupRealtimeSync();
        loadData();
      }
    });
  }

  /// 실시간 동기화 설정
  void _setupRealtimeSync() {
    try {
      // 기존 구독 해제
      _eventDataSubscription?.cancel();
      // 템플릿 변경은 dataChanges를 통해 전역 구독
      final syncService = ref.read(realtimeSyncServiceProvider);
      _userDataSubscription?.cancel();
      _userDataSubscription = syncService.dataChanges.listen((change) async {
        if (change.collectionName != 'checklist_templates') return;
        try {
          final data = change.data ?? {};
          switch (change.changeType) {
            case RealtimeChangeType.added:
            case RealtimeChangeType.modified:
              final template = ChecklistTemplate(
                id: data['id'] is int ? data['id'] as int : int.tryParse('${data['id']}'),
                title: data['title'] ?? '',
                order: (data['order'] as num?)?.toInt() ?? 0,
                isActive: (data['isActive'] as num?)?.toInt() == 1,
                createdAt: data['createdAt'] != null ? DateTime.parse(data['createdAt']) : DateTime.now(),
                updatedAt: data['updatedAt'] != null ? DateTime.parse(data['updatedAt']) : DateTime.now(),
              );
              // 로컬 upsert 시도: 존재하면 업데이트, 없으면 삽입
              try {
                await repository.updateTemplate(template);
              } catch (_) {
                await repository.insertTemplate(template);
              }
              break;
            case RealtimeChangeType.removed:
              final idVal = data['id'];
              final tid = idVal is int ? idVal : int.tryParse('$idVal');
              if (tid != null) {
                await repository.deleteTemplate(tid);
              }
              break;
          }
          // 상태 새로고침 (템플릿만)
          final templates = await repository.getAllTemplates();
          state = state.copyWith(templates: templates);
        } catch (e) {
          LoggerUtils.logWarning('체크리스트 템플릿 실시간 반영 경고: $e', tag: _tag);
          // 문제 시 풀 리프레시
          await ref.read(dataSyncServiceProvider).downloadChecklistTemplatesFromFirebase();
          await loadData(showLoading: false);
        }
      });

      // 현재 워크스페이스의 체크리스트 아이템 실시간 동기화
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace != null) {
        // 서비스 초기화 및 이벤트 구독 보장 (중복 구독은 서비스 내부에서 안전하게 처리)
        Future.microtask(() async {
          try {
            if (!syncService.isInitialized.value) {
              await syncService.initialize();
            }
            await syncService.subscribeToEvent(currentWorkspace.id);

            final eventDataStream = syncService.getEventDataStream(currentWorkspace.id);
            if (eventDataStream != null) {
              _eventDataSubscription = eventDataStream.listen((eventData) {
                _handleEventDataUpdate(eventData);
              });
            }
          } catch (e) {
            LoggerUtils.logError('체크리스트 실시간 구독 설정 실패', tag: _tag, error: e);
          }
        });
      }

      LoggerUtils.logInfo('체크리스트 실시간 동기화 설정 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 실시간 동기화 설정 실패', tag: _tag, error: e);
    }
  }

  /// 이벤트 데이터 업데이트 처리
  void _handleEventDataUpdate(EventRealtimeData eventData) async {
    try {
      LoggerUtils.logInfo('체크리스트 아이템 실시간 업데이트: ${eventData.checklistItems.length}개', tag: _tag);

      // 현재 상태의 아이템과 비교하여 변경사항만 처리
      final prevItems = state.items;
      final newItems = eventData.checklistItems;

      if (prevItems.length != newItems.length || !_areItemListsEqual(prevItems, newItems)) {
        // 1) UI 즉시 반영(로컬 DB 로드를 기다리지 않음)
        state = state.copyWith(items: newItems);

        // 2) 백그라운드로 변경된 항목만 최소 upsert하여 로컬 DB 정합성 유지
        //    (DB 사용량 최소화: 변경된 키만 기록)
        // ignore: unawaited_futures
        _persistChecklistDiff(prevItems, newItems);
      }
    } catch (e) {
      LoggerUtils.logError('체크리스트 이벤트 데이터 업데이트 처리 실패', tag: _tag, error: e);
    }
  }

  /// 변경된 체크리스트 아이템만 로컬 DB에 반영 (최소 upsert)
  Future<void> _persistChecklistDiff(List<ChecklistItem> prevItems, List<ChecklistItem> newItems) async {
    try {
      final byKey = <String, ChecklistItem>{
        for (final item in prevItems) '${item.templateId}_${item.eventId}': item
      };

      for (final item in newItems) {
        final key = '${item.templateId}_${item.eventId}';
        final prev = byKey[key];
        final changed = prev == null || prev.isChecked != item.isChecked || (prev.checkedAt?.millisecondsSinceEpoch != item.checkedAt?.millisecondsSinceEpoch);
        if (changed) {
          await repository.upsertItem(item);
        }
      }
    } catch (e) {
      LoggerUtils.logWarning('체크리스트 로컬 동기화(upsert) 중 경고', tag: _tag, error: e);
    }
  }

  /// 체크리스트 아이템 리스트 비교
  bool _areItemListsEqual(List<ChecklistItem> list1, List<ChecklistItem> list2) {
    if (list1.length != list2.length) return false;

    for (int i = 0; i < list1.length; i++) {
      final item1 = list1[i];
      final item2 = list2.firstWhere(
        (item) => item.templateId == item1.templateId && item.eventId == item1.eventId,
        orElse: () => ChecklistItem(templateId: -1, eventId: -1),
      );

      if (item2.templateId == -1 || item1.isChecked != item2.isChecked) {
        return false;
      }
    }

    return true;
  }

  /// 체크리스트 데이터 로드
  Future<void> loadData({bool showLoading = true}) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      LoggerUtils.logWarning('현재 워크스페이스가 없어 체크리스트 로드 불가', tag: _tag);
      state = state.copyWith(
        isLoading: false,
        error: '워크스페이스가 설정되지 않았습니다. 행사를 먼저 선택해주세요.',
        templates: [],
        items: [],
      );
      return;
    }

    if (showLoading) {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      LoggerUtils.logInfo('체크리스트 데이터 로드 시작 - 워크스페이스: ${currentWorkspace.name}', tag: _tag);

      // 1단계: 로컬에서 템플릿 로드
      List<ChecklistTemplate> templates = await repository.getAllTemplates();

      // 2단계: 로컬에 템플릿이 없으면 서버에서 가져오기
      if (templates.isEmpty) {
        LoggerUtils.logInfo('로컬에 체크리스트 템플릿이 없음 - 서버에서 데이터 가져오기 시도', tag: _tag);

        try {
          final dataSyncService = ref.read(dataSyncServiceProvider);
          await dataSyncService.downloadChecklistTemplatesFromFirebase();
          templates = await repository.getAllTemplates();
          LoggerUtils.logInfo('서버에서 체크리스트 템플릿 다운로드 완료: ${templates.length}개', tag: _tag);
        } catch (e) {
          LoggerUtils.logWarning('서버에서 체크리스트 템플릿 가져오기 실패', tag: _tag, error: e);
        }
      }

      // 3단계: 현재 행사의 체크리스트 아이템 로드
      final items = await repository.getItemsByEventId(currentWorkspace.id);

      state = state.copyWith(
        templates: templates,
        items: items,
        isLoading: false,
        error: null,
      );

      LoggerUtils.logInfo('체크리스트 데이터 로드 완료 - 템플릿: ${templates.length}개, 아이템: ${items.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 데이터 로드 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 체크리스트 템플릿 추가
  Future<void> addTemplate(ChecklistTemplate template) async {
    try {
      state = state.copyWith(isUpdating: true);

      LoggerUtils.logInfo('체크리스트 템플릿 등록 시작: ${template.title}', tag: _tag);

      // 1. 로컬 DB에 저장하고 생성된 ID 받기
      final insertedId = await repository.insertTemplate(template);
      LoggerUtils.logInfo('체크리스트 템플릿 로컬 저장 성공: ${template.title}, ID: $insertedId', tag: _tag);

      // 2. ID가 포함된 템플릿으로 업데이트
      final templateWithId = template.copyWith(id: insertedId);

      // 3. Firebase에 즉시 업로드
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleChecklistTemplate(templateWithId);
        LoggerUtils.logInfo('체크리스트 템플릿 Firebase 업로드 성공: ${template.title}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('체크리스트 템플릿 Firebase 업로드 실패: ${template.title}', tag: _tag, error: e);
      }

      // 4. 로컬 데이터 다시 로드
      await loadData(showLoading: false);

      // 5. 관련 Provider들에게 체크리스트 템플릿 추가 알림
      _notifyRelatedProviders();

      state = state.copyWith(isUpdating: false);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 추가 실패', tag: _tag, error: e);
      state = state.copyWith(isUpdating: false, error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 템플릿 수정
  Future<void> updateTemplate(ChecklistTemplate template) async {
    try {
      state = state.copyWith(isUpdating: true);

      LoggerUtils.logInfo('체크리스트 템플릿 수정 시작: ${template.title}', tag: _tag);

      // 1. 로컬 DB 업데이트
      await repository.updateTemplate(template);

      // 2. Firebase에 즉시 업로드
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleChecklistTemplate(template);
        LoggerUtils.logInfo('체크리스트 템플릿 Firebase 업데이트 성공: ${template.title}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('체크리스트 템플릿 Firebase 업데이트 실패: ${template.title}', tag: _tag, error: e);
      }

      // 3. 로컬 데이터 다시 로드
      await loadData(showLoading: false);

      // 4. 관련 Provider들에게 체크리스트 템플릿 수정 알림
      _notifyRelatedProviders();

      state = state.copyWith(isUpdating: false);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 수정 실패', tag: _tag, error: e);
      state = state.copyWith(isUpdating: false, error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 템플릿 삭제
  Future<void> deleteTemplate(int templateId) async {
    try {
      state = state.copyWith(isUpdating: true);

      LoggerUtils.logInfo('체크리스트 템플릿 삭제 시작: $templateId', tag: _tag);



      // 1. 로컬 DB에서 삭제
      await repository.deleteTemplate(templateId);
      await repository.deleteItemsByTemplateId(templateId);

      // 2. Firebase에서 삭제
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.deleteChecklistTemplate(templateId);
        LoggerUtils.logInfo('체크리스트 템플릿 Firebase 삭제 성공: $templateId', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('체크리스트 템플릿 Firebase 삭제 실패: $templateId', tag: _tag, error: e);
      }

      // 3. 로컬 데이터 다시 로드
      await loadData(showLoading: false);

      // 4. 관련 Provider들에게 체크리스트 템플릿 삭제 알림
      _notifyRelatedProviders();

      state = state.copyWith(isUpdating: false);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 삭제 실패', tag: _tag, error: e);
      state = state.copyWith(isUpdating: false, error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 아이템 체크 토글 (로컬 상태만 변경)
  void toggleItemCheckLocal(int templateId) {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      LoggerUtils.logError('현재 워크스페이스가 없어 체크리스트 아이템을 토글할 수 없습니다', tag: _tag);
      return;
    }

    try {
      LoggerUtils.logInfo('체크리스트 아이템 로컬 체크 토글: templateId=$templateId', tag: _tag);

      // 현재 상태 확인 (변경사항 포함)
      final currentState = state.getEffectiveCheckState(templateId, currentWorkspace.id);
      final newState = !currentState;

      // 변경사항에 추가
      final newPendingChanges = Map<int, bool>.from(state.pendingChanges);
      newPendingChanges[templateId] = newState;

      state = state.copyWith(pendingChanges: newPendingChanges);

      LoggerUtils.logInfo('체크리스트 아이템 로컬 상태 변경 완료: templateId=$templateId, newState=$newState', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 아이템 로컬 토글 실패', tag: _tag, error: e);
    }
  }

  /// 변경사항을 서버에 저장
  Future<void> saveChanges() async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null || !state.hasChanges) {
      return;
    }

    try {
      state = state.copyWith(isSaving: true);
      LoggerUtils.logInfo('체크리스트 변경사항 저장 시작: ${state.pendingChanges.length}개', tag: _tag);

      final dataSyncService = ref.read(dataSyncServiceProvider);

      // 각 변경사항을 DB에 적용하고 Firebase에 업로드
      for (final entry in state.pendingChanges.entries) {
        final templateId = entry.key;
        final isChecked = entry.value;

        try {
          // 1. 로컬 DB 업데이트
          await repository.setItemCheckState(templateId, currentWorkspace.id, isChecked);

          // 2. Firebase에 동기화
          final items = await repository.getItemsByEventId(currentWorkspace.id);
          final item = items.where((i) => i.templateId == templateId).firstOrNull;
          if (item != null) {
            await dataSyncService.uploadSingleChecklistItem(item);
          }

          LoggerUtils.logDebug('체크리스트 아이템 저장 완료: templateId=$templateId, isChecked=$isChecked', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('체크리스트 아이템 저장 실패: templateId=$templateId', tag: _tag, error: e);
          // 개별 아이템 저장 실패해도 계속 진행
        }
      }

      // 변경사항 초기화 및 데이터 다시 로드
      state = state.copyWith(
        pendingChanges: {},
        isSaving: false,
      );

      await loadData(showLoading: false);

      // 관련 Provider들에게 체크리스트 아이템 변경 알림
      _notifyRelatedProviders();

      LoggerUtils.logInfo('체크리스트 변경사항 저장 완료', tag: _tag);
    } catch (e) {
      state = state.copyWith(isSaving: false);
      LoggerUtils.logError('체크리스트 변경사항 저장 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 변경사항 취소
  void discardChanges() {
    if (state.hasChanges) {
      state = state.copyWith(pendingChanges: {});
      LoggerUtils.logInfo('체크리스트 변경사항 취소됨', tag: _tag);
    }
  }

  /// 특정 템플릿의 체크 상태 조회
  bool isTemplateChecked(int templateId) {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return false;

    return state.items.any((item) =>
        item.templateId == templateId &&
        item.eventId == currentWorkspace.id &&
        item.isChecked);
  }

  /// 관련 Provider들에게 체크리스트 업데이트 알림
  void _notifyRelatedProviders() {
    try {
      // 백그라운드에서 관련 Provider들 갱신 (UI 블로킹 방지)
      Future.microtask(() async {
        try {
          // 체크리스트는 다른 Provider들과 직접적인 연관성이 적으므로
          // 현재는 로그만 출력하고 필요시 추가 구현
          LoggerUtils.logInfo('체크리스트 관련 Provider들 갱신 완료', tag: _tag);
        } catch (e) {
          LoggerUtils.logError('체크리스트 관련 Provider 갱신 실패', tag: _tag, error: e);
        }
      });
    } catch (e) {
      LoggerUtils.logError('체크리스트 관련 Provider 알림 실패', tag: _tag, error: e);
    }
  }
}

// Provider 정의
final checklistRepositoryProvider = Provider<ChecklistRepository>((ref) {
  final databaseService = ref.read(databaseServiceProvider);
  return ChecklistRepository(databaseService);
});

final checklistNotifierProvider = StateNotifierProvider<ChecklistNotifier, ChecklistState>((ref) {
  final repository = ref.read(checklistRepositoryProvider);
  return ChecklistNotifier(repository, ref);
});
