<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>계정 삭제 요청 - 바라부스매니저</title>
    <link rel="icon" type="image/png" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #FFFFFF;
            color: #1F2937;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header Section */
        .header-section {
            background-color: #F8F9FA;
            padding: 60px 24px;
            text-align: center;
        }

        .header-icon {
            width: 80px;
            height: 80px;
            background: rgba(37, 99, 235, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 40px;
            color: #2563EB;
        }

        .header-title {
            font-size: 28px;
            font-weight: 800;
            color: #1F2937;
            margin-bottom: 12px;
        }

        .header-description {
            font-size: 16px;
            color: #6B7280;
            line-height: 1.6;
        }

        /* Content Section */
        .content-section {
            padding: 60px 24px;
        }

        .method-card {
            background: white;
            border: 1px solid #E5E7EB;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.02);
            display: flex;
            align-items: flex-start;
        }

        .method-number {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: 800;
            margin-right: 16px;
            flex-shrink: 0;
        }

        .method-content {
            flex: 1;
        }

        .method-title {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }

        .method-icon {
            margin-right: 8px;
            color: #667eea;
            font-size: 20px;
        }

        .method-description {
            color: #6B7280;
            line-height: 1.5;
        }

        .contact-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            color: white;
            margin-top: 32px;
        }

        .contact-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .contact-email {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 16px;
            color: #FFD700;
        }

        .contact-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
        }

        .contact-info-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .contact-info-text {
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.5;
        }

        /* Footer */
        .footer {
            background: white;
            border-top: 1px solid #E5E7EB;
            padding: 40px 24px;
            text-align: center;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
        }

        .footer-logo-icon {
            padding: 8px;
            background: linear-gradient(135deg, #E09A74, #D08052);
            border-radius: 8px;
            margin-right: 12px;
            font-size: 20px;
        }

        .footer-logo-text {
            font-size: 18px;
            font-weight: 700;
            color: #1F2937;
        }

        .footer-info {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }

        .footer-info-item {
            font-size: 14px;
            color: #6B7280;
        }

        .footer-divider {
            border: none;
            border-top: 1px solid #E5E7EB;
            margin: 24px 0 16px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 24px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .footer-link {
            color: #6B7280;
            text-decoration: none;
            font-size: 14px;
        }

        .footer-link:hover {
            color: #E09A74;
        }

        .copyright {
            font-size: 12px;
            color: #9CA3AF;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-title {
                font-size: 24px;
            }
            
            .header-description {
                font-size: 14px;
            }
            
            .method-card {
                flex-direction: column;
                text-align: center;
            }
            
            .method-number {
                margin: 0 auto 16px;
            }
            
            .method-title {
                justify-content: center;
            }
            
            .footer-info {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }
            
            .footer-links {
                flex-direction: column;
                align-items: center;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <section class="header-section">
        <div class="container">
            <div class="header-icon">🗑️</div>
            <h1 class="header-title">계정 삭제 요청</h1>
            <p class="header-description">
                바라 부스 매니저 앱에서 회원 탈퇴 및 개인정보 삭제를 원하시는 경우,<br>
                아래 절차를 따라 요청해 주세요.
            </p>
        </div>
    </section>

    <!-- Content Section -->
    <section class="content-section">
        <div class="container">
            <!-- 방법 1: 앱 내에서 계정 삭제 -->
            <div class="method-card">
                <div class="method-number">1</div>
                <div class="method-content">
                    <div class="method-title">
                        <span class="method-icon">📱</span>
                        앱 내에서 계정 삭제
                    </div>
                    <div class="method-description">
                        바라 부스 매니저 앱 → 설정 → 계정 관리 → 계정 삭제 메뉴를 이용해 주세요.
                    </div>
                </div>
            </div>

            <!-- 방법 2: 이메일로 삭제 요청 -->
            <div class="method-card">
                <div class="method-number">2</div>
                <div class="method-content">
                    <div class="method-title">
                        <span class="method-icon">📧</span>
                        이메일로 삭제 요청
                    </div>
                    <div class="method-description">
                        앱에서 삭제가 어려운 경우, 아래 이메일로 직접 연락 주세요.
                    </div>
                </div>
            </div>

            <!-- 연락처 정보 -->
            <div class="contact-section">
                <div class="contact-title">고객지원 연락처</div>
                <div class="contact-email"><EMAIL></div>
                
                <div class="contact-info">
                    <div class="contact-info-title">계정 삭제 요청 시 다음 정보를 포함해 주세요:</div>
                    <div class="contact-info-text">
                        • 등록된 이메일 주소<br>
                        • 삭제 요청 사유 (선택사항)<br>
                        • 본인 확인을 위한 추가 정보
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">


            <!-- 회사 정보 -->
            <div class="footer-info">
                <div class="footer-info-item"><strong>상호명:</strong> 바라부스매니저</div>
                <div class="footer-info-item"><strong>대표:</strong> 권태영</div>
                <div class="footer-info-item"><strong>전화:</strong> 070-8080-4308</div>
                <div class="footer-info-item"><strong>이메일:</strong> <EMAIL></div>
            </div>
            
            <div class="footer-info">
                <div class="footer-info-item"><strong>주소:</strong> 서울 강서구 마곡동 마곡중앙로 36 1504동 1301호</div>
                <div class="footer-info-item"><strong>사업자등록번호:</strong> 184-53-01069</div>
                <div class="footer-info-item"><strong>통신판매업신고:</strong> 제2025-서울강서-2441호</div>
                <div class="footer-info-item"><strong>개인정보관리책임자:</strong> 권태영</div>
            </div>
            
            <hr class="footer-divider">
            
            <div class="footer-links">
                <a href="/" class="footer-link">홈</a>
                <a href="/privacy-policy.html" class="footer-link">개인정보 처리방침</a>
                <a href="/account-deletion.html" class="footer-link">계정 삭제</a>
                <a href="/support.html" class="footer-link">고객지원</a>
            </div>
            
            <div class="copyright">
                © 2025 바라부스매니저. All rights reserved.
            </div>
        </div>
    </footer>
</body>
</html>
