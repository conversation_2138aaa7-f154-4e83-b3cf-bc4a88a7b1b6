plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
    id("com.google.gms.google-services")
}

import java.io.FileInputStream
import java.util.Properties

// 🔒 최고 보안 수준 키스토어 설정
val keystoreProperties = Properties()

// 1순위: 환경 변수 (가장 안전)
val keystorePassword = System.getenv("PARABARA_KEYSTORE_PASSWORD") 
val keyPassword = System.getenv("PARABARA_KEY_PASSWORD")
val keyAlias = System.getenv("PARABARA_KEY_ALIAS") ?: "parabara-release"
val keystoreFile = System.getenv("PARABARA_KEYSTORE_FILE") ?: "app-release-key.jks"

// 2순위: 빌드 시 시스템 프로퍼티
val fallbackKeystorePassword = System.getProperty("keystore.password")
val fallbackKeyPassword = System.getProperty("key.password")

// 환경 변수가 있으면 사용, 없으면 빌드 실패 (보안 강화)
if (keystorePassword != null && keyPassword != null) {
    keystoreProperties["storePassword"] = keystorePassword
    keystoreProperties["keyPassword"] = keyPassword
    keystoreProperties["keyAlias"] = keyAlias
    keystoreProperties["storeFile"] = keystoreFile
    println("✅ 환경 변수에서 키스토어 정보 로드됨")
} else if (fallbackKeystorePassword != null && fallbackKeyPassword != null) {
    keystoreProperties["storePassword"] = fallbackKeystorePassword
    keystoreProperties["keyPassword"] = fallbackKeyPassword  
    keystoreProperties["keyAlias"] = keyAlias
    keystoreProperties["storeFile"] = keystoreFile
    println("✅ 시스템 프로퍼티에서 키스토어 정보 로드됨")
} else {
    println("❌ 키스토어 정보를 찾을 수 없습니다. 환경 변수를 설정하세요:")
    println("   PARABARA_KEYSTORE_PASSWORD")
    println("   PARABARA_KEY_PASSWORD") 
    println("   PARABARA_KEY_ALIAS (선택사항)")
    println("   PARABARA_KEYSTORE_FILE (선택사항)")
    // throw GradleException("키스토어 정보가 설정되지 않았습니다!")
}

android {
    namespace = "com.blue.parabara"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    buildFeatures {
        buildConfig = true
    }

    // 패키징 최적화 - 불필요한 파일 제거
    packagingOptions {
        resources {
            excludes += listOf(
                "META-INF/DEPENDENCIES",
                "META-INF/LICENSE", 
                "META-INF/LICENSE.txt",
                "META-INF/NOTICE",
                "META-INF/NOTICE.txt"
            )
        }
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.blue.parabara"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 28
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    // 서명 설정
    signingConfigs {
        getByName("debug") {
            keyAlias = "parabara-debug"
            keyPassword = "android"
            storeFile = file("../parabara-debug-keystore.jks")
            storePassword = "android"
        }
        create("release") {
                keyAlias = keystoreProperties["keyAlias"] as String?
                keyPassword = keystoreProperties["keyPassword"] as String?
                storeFile = file("../parabara-keystore.jks")
                storePassword = keystoreProperties["storePassword"] as String?
        }
    }

    buildTypes {
        debug {
            // 디버그용 서명 설정 적용
            signingConfig = signingConfigs.getByName("debug")
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            isDebuggable = true
            // Firebase 디버그 모드 활성화
            buildConfigField("boolean", "DEBUG_MODE", "true")
        }
        release {
            // 릴리즈 빌드용 서명 설정
            signingConfig = signingConfigs.getByName("release")
            isMinifyEnabled = true
            isShrinkResources = true // 사용하지 않는 리소스 제거
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")

            // 추가 최적화 설정
            isDebuggable = false
            isJniDebuggable = false
            isRenderscriptDebuggable = false
            isPseudoLocalesEnabled = false
            buildConfigField("boolean", "DEBUG_MODE", "false")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation(platform("com.google.firebase:firebase-bom:33.1.2"))
    implementation("com.google.firebase:firebase-analytics")
}
