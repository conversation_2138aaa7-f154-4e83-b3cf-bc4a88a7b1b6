[{"File": "providers\\base_provider.dart", "Base": "base_provider.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "providers\\current_event_provider.dart", "Base": "current_event_provider.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "providers\\product_crud_logic.dart", "Base": "product_crud_logic.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "providers\\product_filter_sort_logic.dart", "Base": "product_filter_sort_logic.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "providers\\product_search_logic.dart", "Base": "product_search_logic.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "providers\\realtime_bridge_provider.dart", "Base": "realtime_bridge_provider.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "providers\\service_provider.dart", "Base": "service_provider.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "repositories\\base_repository.dart", "Base": "base_repository.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "screens\\prepayment\\edit_prepayment_screen.dart", "Base": "edit_prepayment_screen.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "screens\\prepayment\\prepayment_detail_screen.dart", "Base": "prepayment_detail_screen.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "screens\\sale\\register_sale_screen.dart", "Base": "register_sale_screen.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "screens\\sale\\sale_business_logic.dart", "Base": "sale_business_logic.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "screens\\sale\\sale_management_screen.dart", "Base": "sale_management_screen.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "screens\\sales_log\\sales_history_screen.dart", "Base": "sales_history_screen.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "screens\\sales_log\\sales_log_list_business_logic_new.dart", "Base": "sales_log_list_business_logic_new.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "services\\app_check_service.dart", "Base": "app_check_service.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "services\\pdf_export_service_new.dart", "Base": "pdf_export_service_new.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "services\\pdf_helpers.dart", "Base": "pdf_helpers.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "utils\\app_colors_backup.dart", "Base": "app_colors_backup.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "utils\\dimens_new.dart", "Base": "dimens_new.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "widgets\\dialogs\\product_deletion_dialog.dart", "Base": "product_deletion_dialog.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "widgets\\form_validation_feedback.dart", "Base": "form_validation_feedback.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "widgets\\progress_indicator.dart", "Base": "progress_indicator.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "widgets\\unified_filter_dialog.dart", "Base": "unified_filter_dialog.dart", "Main": false, "Part": false, "RefCount": 0, "Suspect": true}, {"File": "models\\category.freezed.dart", "Base": "category.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\category.g.dart", "Base": "category.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\checklist_item.freezed.dart", "Base": "checklist_item.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\checklist_item.g.dart", "Base": "checklist_item.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\checklist_template.freezed.dart", "Base": "checklist_template.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\checklist_template.g.dart", "Base": "checklist_template.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\event_state.freezed.dart", "Base": "event_state.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\event.freezed.dart", "Base": "event.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\event.g.dart", "Base": "event.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\nickname.freezed.dart", "Base": "nickname.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\nickname.g.dart", "Base": "nickname.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\prepayment_product_link.freezed.dart", "Base": "prepayment_product_link.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\prepayment_product_link.g.dart", "Base": "prepayment_product_link.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\prepayment.freezed.dart", "Base": "prepayment.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\prepayment.g.dart", "Base": "prepayment.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\product_sale_stat.freezed.dart", "Base": "product_sale_stat.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\product_sale_stat.g.dart", "Base": "product_sale_stat.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\product.freezed.dart", "Base": "product.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\product.g.dart", "Base": "product.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\purchased_product.freezed.dart", "Base": "purchased_product.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\purchased_product.g.dart", "Base": "purchased_product.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\sale.freezed.dart", "Base": "sale.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\sale.g.dart", "Base": "sale.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\sales_log.freezed.dart", "Base": "sales_log.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\sales_log.g.dart", "Base": "sales_log.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\sales_stat_item.freezed.dart", "Base": "sales_stat_item.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\sales_stat_item.g.dart", "Base": "sales_stat_item.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\seller.freezed.dart", "Base": "seller.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\seller.g.dart", "Base": "seller.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\set_discount_transaction.freezed.dart", "Base": "set_discount_transaction.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\set_discount_transaction.g.dart", "Base": "set_discount_transaction.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\set_discount.freezed.dart", "Base": "set_discount.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\set_discount.g.dart", "Base": "set_discount.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\sync_metadata.freezed.dart", "Base": "sync_metadata.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\sync_metadata.g.dart", "Base": "sync_metadata.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\user_settings.freezed.dart", "Base": "user_settings.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\user_settings.g.dart", "Base": "user_settings.g.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "providers\\set_discount_provider.freezed.dart", "Base": "set_discount_provider.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "repositories\\category_repository.freezed.dart", "Base": "category_repository.freezed.dart", "Main": false, "Part": true, "RefCount": 0, "Suspect": false}, {"File": "models\\product_sale_stat.dart", "Base": "product_sale_stat.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "models\\sync_state.dart", "Base": "sync_state.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "providers\\fallback_data_provider.dart", "Base": "fallback_data_provider.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "providers\\global_sync_state_provider.dart", "Base": "global_sync_state_provider.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "providers\\home_dashboard_filter_provider.dart", "Base": "home_dashboard_filter_provider.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "providers\\product_notifier.dart", "Base": "product_notifier.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "providers\\sale_state.dart", "Base": "sale_state.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "providers\\sales_log\\sales_log_batch_operations.dart", "Base": "sales_log_batch_operations.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "providers\\sales_log\\sales_log_filter_sort.dart", "Base": "sales_log_filter_sort.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "providers\\sales_log\\sales_log_stats.dart", "Base": "sales_log_stats.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "repositories\\category_repository.dart", "Base": "category_repository.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "repositories\\sales_log_stats.dart", "Base": "sales_log_stats.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\auth\\forgot_password_screen.dart", "Base": "forgot_password_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\auth\\login_screen.dart", "Base": "login_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\auth\\nickname_screen.dart", "Base": "nickname_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\auth\\register_screen.dart", "Base": "register_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\auth\\verify_email_screen.dart", "Base": "verify_email_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\checklist\\checklist_edit_dialog.dart", "Base": "checklist_edit_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\checklist\\checklist_screen.dart", "Base": "checklist_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\event\\event_form_screen.dart", "Base": "event_form_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\event\\event_list_screen.dart", "Base": "event_list_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\home\\home_dashboard_screen.dart", "Base": "home_dashboard_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\inventory\\prepayment_tab.dart", "Base": "prepayment_tab.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\inventory\\qr_scan_screen.dart", "Base": "qr_scan_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\onboarding\\event_workspace_onboarding_screen.dart", "Base": "event_workspace_onboarding_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\onboarding\\onboarding_screen.dart", "Base": "onboarding_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\onboarding\\post_onboarding_init_screen.dart", "Base": "post_onboarding_init_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\prepayment\\prepayment_product_link_screen.dart", "Base": "prepayment_product_link_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\prepayment\\prepayment_virtual_product_management_screen.dart", "Base": "prepayment_virtual_product_management_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\product\\manual_bulk_product_registration_screen.dart", "Base": "manual_bulk_product_registration_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\records_and_statistics\\statistics_tab_content.dart", "Base": "statistics_tab_content.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\revenue_goal\\revenue_goal_dialog.dart", "Base": "revenue_goal_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\sale\\fixed_bottom_bar.dart", "Base": "fixed_bottom_bar.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\sale\\order_summary_panel.dart", "Base": "order_summary_panel.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\sale\\sale_ui_components.dart", "Base": "sale_ui_components.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\sales_log\\sales_log_list_business_logic.dart", "Base": "sales_log_list_business_logic.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\sales_log\\sales_log_screen.dart", "Base": "sales_log_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\sales_log\\sales_stats_tab.dart", "Base": "sales_stats_tab.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\seller\\seller_management_screen.dart", "Base": "seller_management_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\set_discount\\set_discount_dialog.dart", "Base": "set_discount_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\set_discount\\set_discount_form_dialog.dart", "Base": "set_discount_form_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\settings\\my_page_screen.dart", "Base": "my_page_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\settings\\payment_methods_dialog.dart", "Base": "payment_methods_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\splash\\splash_screen.dart", "Base": "splash_screen.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\statistics\\statistics_business_logic.dart", "Base": "statistics_business_logic.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\statistics\\statistics_calculator.dart", "Base": "statistics_calculator.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\statistics\\statistics_controller.dart", "Base": "statistics_controller.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\statistics\\statistics_filter.dart", "Base": "statistics_filter.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "screens\\statistics\\statistics_ui_components.dart", "Base": "statistics_ui_components.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "services\\excel_export_service.dart", "Base": "excel_export_service.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "services\\set_discount_service.dart", "Base": "set_discount_service.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "services\\sync_metadata_service.dart", "Base": "sync_metadata_service.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "services\\user_settings_service.dart", "Base": "user_settings_service.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "services\\workspace_data_manager.dart", "Base": "workspace_data_manager.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "utils\\app_check_config.dart", "Base": "app_check_config.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "utils\\app_check_utils.dart", "Base": "app_check_utils.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "utils\\business_validators.dart", "Base": "business_validators.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "utils\\firebase_service_wrapper.dart", "Base": "firebase_service_wrapper.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "utils\\logout_manager.dart", "Base": "logout_manager.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "utils\\responsive_text_utils.dart", "Base": "responsive_text_utils.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "utils\\safe_dialog_utils.dart", "Base": "safe_dialog_utils.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "utils\\validation_rules.dart", "Base": "validation_rules.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\adaptive_donut_chart.dart", "Base": "adaptive_donut_chart.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\excel_preview_dialog.dart", "Base": "excel_preview_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\pdf_export_type_selection_dialog.dart", "Base": "pdf_export_type_selection_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\pdf_preview_dialog.dart", "Base": "pdf_preview_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\profile_avatar_widget.dart", "Base": "profile_avatar_widget.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\sale_confirmation_dialog.dart", "Base": "sale_confirmation_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\sales_record_filter_dialog.dart", "Base": "sales_record_filter_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\slider_quantity_widget.dart", "Base": "slider_quantity_widget.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "widgets\\statistics_filter_dialog.dart", "Base": "statistics_filter_dialog.dart", "Main": false, "Part": false, "RefCount": 1, "Suspect": false}, {"File": "models\\event_state.dart", "Base": "event_state.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "models\\nickname.dart", "Base": "nickname.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "models\\payment_method.dart", "Base": "payment_method.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "models\\purchased_product.dart", "Base": "purchased_product.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "providers\\base_async_notifier.dart", "Base": "base_async_notifier.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "providers\\payment_methods_provider.dart", "Base": "payment_methods_provider.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "providers\\product_state.dart", "Base": "product_state.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "providers\\retry_policy.dart", "Base": "retry_policy.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "providers\\revenue_goal_provider.dart", "Base": "revenue_goal_provider.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "providers\\sale_provider.dart", "Base": "sale_provider.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "providers\\sales_log\\sales_log_crud.dart", "Base": "sales_log_crud.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "repositories\\checklist_repository.dart", "Base": "checklist_repository.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "repositories\\prepayment_product_link_repository.dart", "Base": "prepayment_product_link_repository.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "repositories\\revenue_goal_repository.dart", "Base": "revenue_goal_repository.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "repositories\\sales_log_crud.dart", "Base": "sales_log_crud.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "repositories\\set_discount_repository.dart", "Base": "set_discount_repository.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "screens\\excel\\excel_import_screen.dart", "Base": "excel_import_screen.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "screens\\records_and_statistics\\records_and_statistics_screen.dart", "Base": "records_and_statistics_screen.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "screens\\sale\\sale_screen.dart", "Base": "sale_screen.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "screens\\sales_log\\sales_log_list_tab.dart", "Base": "sales_log_list_tab.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "screens\\sync\\sync_confirmation_screen.dart", "Base": "sync_confirmation_screen.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "services\\data_sync_service.dart", "Base": "data_sync_service.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "services\\link_service.dart", "Base": "link_service.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\common_utils.dart", "Base": "common_utils.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\database_optimizer.dart", "Base": "database_optimizer.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\local_data_cleaner.dart", "Base": "local_data_cleaner.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\memory_manager.dart", "Base": "memory_manager.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\merge_util.dart", "Base": "merge_util.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\mobile_performance_utils.dart", "Base": "mobile_performance_utils.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\object_pool.dart", "Base": "object_pool.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\product_display_utils.dart", "Base": "product_display_utils.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\provider_error_handler.dart", "Base": "provider_error_handler.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "utils\\state_sync_manager.dart", "Base": "state_sync_manager.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "widgets\\event_image.dart", "Base": "event_image.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "widgets\\excel_import_mode_dialog.dart", "Base": "excel_import_mode_dialog.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "widgets\\registration_complete_page.dart", "Base": "registration_complete_page.dart", "Main": false, "Part": false, "RefCount": 2, "Suspect": false}, {"File": "models\\user_settings.dart", "Base": "user_settings.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "providers\\checklist_provider.dart", "Base": "checklist_provider.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "providers\\event_provider.dart", "Base": "event_provider.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "providers\\prepayment_state.dart", "Base": "prepayment_state.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "providers\\set_discount_provider.dart", "Base": "set_discount_provider.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "repositories\\sale_repository.dart", "Base": "sale_repository.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "screens\\inventory\\inventory_screen.dart", "Base": "inventory_screen.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "screens\\product\\register_product_screen.dart", "Base": "register_product_screen.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "screens\\sales_log\\sales_log_list_filter_logic.dart", "Base": "sales_log_list_filter_logic.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "screens\\sales_log\\sales_log_list_ui_components.dart", "Base": "sales_log_list_ui_components.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "screens\\settings\\settings_screen.dart", "Base": "settings_screen.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "screens\\statistics\\statistics_screen.dart", "Base": "statistics_screen.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "screens\\statistics\\statistics_state.dart", "Base": "statistics_state.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "services\\differential_sync_service.dart", "Base": "differential_sync_service.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "utils\\excel_processor.dart", "Base": "excel_processor.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "utils\\firebase_upload_utils.dart", "Base": "firebase_upload_utils.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "utils\\validation_result.dart", "Base": "validation_result.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "utils\\validators.dart", "Base": "validators.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "widgets\\loading_indicator.dart", "Base": "loading_indicator.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "widgets\\product_image.dart", "Base": "product_image.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "widgets\\sales_record_detail_dialog.dart", "Base": "sales_record_detail_dialog.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "widgets\\skeleton_loading.dart", "Base": "skeleton_loading.dart", "Main": false, "Part": false, "RefCount": 3, "Suspect": false}, {"File": "models\\checklist_item.dart", "Base": "checklist_item.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "models\\event_sort_option.dart", "Base": "event_sort_option.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "models\\revenue_goal.dart", "Base": "revenue_goal.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "models\\set_discount_transaction.dart", "Base": "set_discount_transaction.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "models\\sync_metadata.dart", "Base": "sync_metadata.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "repositories\\event_repository.dart", "Base": "event_repository.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "repositories\\settings_repository.dart", "Base": "settings_repository.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "screens\\prepayment\\register_prepayment_screen.dart", "Base": "register_prepayment_screen.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "services\\event_workspace_manager.dart", "Base": "event_workspace_manager.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "utils\\date_utils.dart", "Base": "date_utils.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "utils\\device_utils.dart", "Base": "device_utils.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "utils\\error_utils.dart", "Base": "error_utils.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "widgets\\modern_dialog_components.dart", "Base": "modern_dialog_components.dart", "Main": false, "Part": false, "RefCount": 4, "Suspect": false}, {"File": "models\\checklist_template.dart", "Base": "checklist_template.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "providers\\prepayment_product_link_provider.dart", "Base": "prepayment_product_link_provider.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "providers\\sales_log\\sales_log_state.dart", "Base": "sales_log_state.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "repositories\\prepayment_repository.dart", "Base": "prepayment_repository.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "services\\pdf_export_service.dart", "Base": "pdf_export_service.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "utils\\image_cache.dart", "Base": "image_cache.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "utils\\image_utils.dart", "Base": "image_utils.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "utils\\sql_utils.dart", "Base": "sql_utils.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "widgets\\image_crop_widget.dart", "Base": "image_crop_widget.dart", "Main": false, "Part": false, "RefCount": 5, "Suspect": false}, {"File": "models\\prepayment_sort_order.dart", "Base": "prepayment_sort_order.dart", "Main": false, "Part": false, "RefCount": 6, "Suspect": false}, {"File": "repositories\\seller_repository.dart", "Base": "seller_repository.dart", "Main": false, "Part": false, "RefCount": 6, "Suspect": false}, {"File": "repositories\\set_discount_transaction_repository.dart", "Base": "set_discount_transaction_repository.dart", "Main": false, "Part": false, "RefCount": 6, "Suspect": false}, {"File": "utils\\batch_processor.dart", "Base": "batch_processor.dart", "Main": false, "Part": false, "RefCount": 6, "Suspect": false}, {"File": "utils\\cancellation_token.dart", "Base": "cancellation_token.dart", "Main": false, "Part": false, "RefCount": 6, "Suspect": false}, {"File": "utils\\offline_task.dart", "Base": "offline_task.dart", "Main": false, "Part": false, "RefCount": 6, "Suspect": false}, {"File": "widgets\\unsaved_changes_dialog.dart", "Base": "unsaved_changes_dialog.dart", "Main": false, "Part": false, "RefCount": 6, "Suspect": false}, {"File": "models\\sale.dart", "Base": "sale.dart", "Main": false, "Part": false, "RefCount": 7, "Suspect": false}, {"File": "models\\sales_stat_item.dart", "Base": "sales_stat_item.dart", "Main": false, "Part": false, "RefCount": 7, "Suspect": false}, {"File": "utils\\image_sync_utils.dart", "Base": "image_sync_utils.dart", "Main": false, "Part": false, "RefCount": 7, "Suspect": false}, {"File": "models\\prepayment_product_link.dart", "Base": "prepayment_product_link.dart", "Main": false, "Part": false, "RefCount": 8, "Suspect": false}, {"File": "models\\product_sort_option.dart", "Base": "product_sort_option.dart", "Main": false, "Part": false, "RefCount": 8, "Suspect": false}, {"File": "models\\sales_log_display_item.dart", "Base": "sales_log_display_item.dart", "Main": false, "Part": false, "RefCount": 8, "Suspect": false}, {"File": "providers\\prepayment_virtual_product_provider.dart", "Base": "prepayment_virtual_product_provider.dart", "Main": false, "Part": false, "RefCount": 8, "Suspect": false}, {"File": "providers\\settings_provider.dart", "Base": "settings_provider.dart", "Main": false, "Part": false, "RefCount": 8, "Suspect": false}, {"File": "utils\\orientation_helper.dart", "Base": "orientation_helper.dart", "Main": false, "Part": false, "RefCount": 8, "Suspect": false}, {"File": "widgets\\confirmation_dialog.dart", "Base": "confirmation_dialog.dart", "Main": false, "Part": false, "RefCount": 8, "Suspect": false}, {"File": "widgets\\onboarding_components.dart", "Base": "onboarding_components.dart", "Main": false, "Part": false, "RefCount": 9, "Suspect": false}, {"File": "models\\category.dart", "Base": "category.dart", "Main": false, "Part": false, "RefCount": 10, "Suspect": false}, {"File": "models\\prepayment_virtual_product.dart", "Base": "prepayment_virtual_product.dart", "Main": false, "Part": false, "RefCount": 10, "Suspect": false}, {"File": "utils\\provider_exception.dart", "Base": "provider_exception.dart", "Main": false, "Part": false, "RefCount": 10, "Suspect": false}, {"File": "models\\set_discount.dart", "Base": "set_discount.dart", "Main": false, "Part": false, "RefCount": 11, "Suspect": false}, {"File": "providers\\base_state.dart", "Base": "base_state.dart", "Main": false, "Part": false, "RefCount": 11, "Suspect": false}, {"File": "repositories\\product_repository.dart", "Base": "product_repository.dart", "Main": false, "Part": false, "RefCount": 11, "Suspect": false}, {"File": "repositories\\sales_log_repository.dart", "Base": "sales_log_repository.dart", "Main": false, "Part": false, "RefCount": 11, "Suspect": false}, {"File": "utils\\dimens.dart", "Base": "dimens.dart", "Main": false, "Part": false, "RefCount": 11, "Suspect": false}, {"File": "utils\\event_workspace_utils.dart", "Base": "event_workspace_utils.dart", "Main": false, "Part": false, "RefCount": 12, "Suspect": false}, {"File": "utils\\network_status.dart", "Base": "network_status.dart", "Main": false, "Part": false, "RefCount": 12, "Suspect": false}, {"File": "utils\\responsive_helper.dart", "Base": "responsive_helper.dart", "Main": false, "Part": false, "RefCount": 12, "Suspect": false}, {"File": "providers\\nickname_provider.dart", "Base": "nickname_provider.dart", "Main": false, "Part": false, "RefCount": 13, "Suspect": false}, {"File": "services\\realtime_sync_service_main.dart", "Base": "realtime_sync_service_main.dart", "Main": false, "Part": false, "RefCount": 13, "Suspect": false}, {"File": "providers\\category_provider.dart", "Base": "category_provider.dart", "Main": false, "Part": false, "RefCount": 14, "Suspect": false}, {"File": "models\\event.dart", "Base": "event.dart", "Main": false, "Part": false, "RefCount": 15, "Suspect": false}, {"File": "providers\\realtime_sync_provider.dart", "Base": "realtime_sync_provider.dart", "Main": false, "Part": false, "RefCount": 15, "Suspect": false}, {"File": "utils\\dialog_theme.dart", "Base": "dialog_theme.dart", "Main": false, "Part": false, "RefCount": 16, "Suspect": false}, {"File": "models\\event_workspace.dart", "Base": "event_workspace.dart", "Main": false, "Part": false, "RefCount": 17, "Suspect": false}, {"File": "models\\seller.dart", "Base": "seller.dart", "Main": false, "Part": false, "RefCount": 17, "Suspect": false}, {"File": "providers\\sales_log_provider.dart", "Base": "sales_log_provider.dart", "Main": false, "Part": false, "RefCount": 17, "Suspect": false}, {"File": "main.dart", "Base": "main.dart", "Main": true, "Part": false, "RefCount": 18, "Suspect": false}, {"File": "providers\\data_sync_provider.dart", "Base": "data_sync_provider.dart", "Main": false, "Part": false, "RefCount": 18, "Suspect": false}, {"File": "providers\\prepayment_provider.dart", "Base": "prepayment_provider.dart", "Main": false, "Part": false, "RefCount": 18, "Suspect": false}, {"File": "providers\\seller_provider.dart", "Base": "seller_provider.dart", "Main": false, "Part": false, "RefCount": 20, "Suspect": false}, {"File": "utils\\currency_utils.dart", "Base": "currency_utils.dart", "Main": false, "Part": false, "RefCount": 21, "Suspect": false}, {"File": "models\\prepayment.dart", "Base": "prepayment.dart", "Main": false, "Part": false, "RefCount": 23, "Suspect": false}, {"File": "providers\\product_provider.dart", "Base": "product_provider.dart", "Main": false, "Part": false, "RefCount": 25, "Suspect": false}, {"File": "utils\\toast_utils.dart", "Base": "toast_utils.dart", "Main": false, "Part": false, "RefCount": 28, "Suspect": false}, {"File": "models\\transaction_type.dart", "Base": "transaction_type.dart", "Main": false, "Part": false, "RefCount": 29, "Suspect": false}, {"File": "models\\sales_log.dart", "Base": "sales_log.dart", "Main": false, "Part": false, "RefCount": 37, "Suspect": false}, {"File": "models\\product.dart", "Base": "product.dart", "Main": false, "Part": false, "RefCount": 38, "Suspect": false}, {"File": "providers\\unified_workspace_provider.dart", "Base": "unified_workspace_provider.dart", "Main": false, "Part": false, "RefCount": 41, "Suspect": false}, {"File": "services\\database_service.dart", "Base": "database_service.dart", "Main": false, "Part": false, "RefCount": 50, "Suspect": false}, {"File": "utils\\app_colors.dart", "Base": "app_colors.dart", "Main": false, "Part": false, "RefCount": 57, "Suspect": false}, {"File": "utils\\logger_utils.dart", "Base": "logger_utils.dart", "Main": false, "Part": false, "RefCount": 97, "Suspect": false}]