import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/admin_models.dart';

/// 관리자 API 서비스
class AdminService {
  static const String _baseUrl = 'https://us-central1-parabara-1a504.cloudfunctions.net';
  static const String _tokenKey = 'admin_token';
  
  // API URLs
  static const Map<String, String> _apiUrls = {
    'adminAuth': 'https://adminauth-kahfshl2oa-uc.a.run.app',
    'adminDashboard': 'https://admindashboard-kahfshl2oa-uc.a.run.app',
    'adminUsers': 'https://adminusers-kahfshl2oa-uc.a.run.app',
    'adminToggleSubscription': '$_baseUrl/adminToggleSubscription',
    'getUserUsageStats': '$_baseUrl/getUserUsageStats',
    'getAdminSystemInfo': '$_baseUrl/getAdminSystemInfo',
  };

  /// 관리자 로그인
  static Future<AdminAuthResponse> login(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse(_apiUrls['adminAuth']!),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'password': password,
        }),
      );

      final data = jsonDecode(response.body);
      final authResponse = AdminAuthResponse.fromJson(data);

      // 토큰 저장
      if (authResponse.success && authResponse.token != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, authResponse.token!);
      }

      return authResponse;
    } catch (e) {
      return AdminAuthResponse(
        success: false,
        message: '로그인 중 오류가 발생했습니다: $e',
      );
    }
  }

  /// 저장된 토큰 가져오기
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// 로그아웃
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }

  /// 인증 헤더 생성
  static Future<Map<String, String>> _getAuthHeaders() async {
    final token = await getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// 대시보드 통계 조회
  static Future<AdminDashboardStats?> getDashboardStats() async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse(_apiUrls['adminDashboard']!),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return AdminDashboardStats.fromJson(data['data']);
        }
      }
      return null;
    } catch (e) {
      print('대시보드 통계 조회 오류: $e');
      return null;
    }
  }

  /// 사용자 목록 조회 (페이지네이션 포함)
  static Future<AdminUsersResponse> getUsers({int page = 1, int limit = 20}) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('${_apiUrls['adminUsers']!}?page=$page&limit=$limit'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          final users = (data['data']['users'] as List)
              .map((user) => AdminUser.fromJson(user))
              .toList();

          // 각 사용자의 사용량 데이터 로드
          await _loadUsageDataForUsers(users);

          final pagination = data['data']['pagination'];
          return AdminUsersResponse(
            users: users,
            pagination: AdminPagination.fromJson(pagination),
          );
        }
      }
      return AdminUsersResponse(users: [], pagination: AdminPagination.empty());
    } catch (e) {
      print('사용자 목록 조회 오류: $e');
      return AdminUsersResponse(users: [], pagination: AdminPagination.empty());
    }
  }

  /// 사용자 목록 조회 (하위 호환성을 위해 유지)
  static Future<List<AdminUser>> getUsersLegacy() async {
    final response = await getUsers();
    return response.users;
  }

  /// 시스템 모니터링 정보 조회
  static Future<Map<String, dynamic>?> getSystemInfo() async {
    try {
      final response = await http.get(
        Uri.parse(_apiUrls['getAdminSystemInfo']!),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return data['data'];
        }
      }
      return null;
    } catch (e) {
      print('시스템 정보 조회 오류: $e');
      return null;
    }
  }

  /// 사용자들의 사용량 데이터 로드
  static Future<void> _loadUsageDataForUsers(List<AdminUser> users) async {
    final futures = users.map((user) async {
      try {
        final response = await http.get(
          Uri.parse('${_apiUrls['getUserUsageStats']}?uid=${user.uid}&days=7'),
        );

        if (response.statusCode == 200) {
          final usageData = jsonDecode(response.body);
          if (usageData['success'] == true) {
            // 사용량 정보 업데이트 (불변 객체이므로 새로 생성해야 하지만 여기서는 간단히 처리)
            // 실제로는 copyWith 메서드를 구현하거나 다른 방식으로 처리해야 함
          }
        }
      } catch (e) {
        print('사용자 ${user.uid} 사용량 로드 오류: $e');
      }
    });

    await Future.wait(futures);
  }

  /// 구독 상태 변경
  static Future<bool> toggleSubscription(String uid, String currentStatus) async {
    try {
      final headers = await _getAuthHeaders();
      final action = currentStatus == 'active' ? 'deactivate' : 'activate';
      
      final response = await http.post(
        Uri.parse(_apiUrls['adminToggleSubscription']!),
        headers: headers,
        body: jsonEncode({
          'uid': uid,
          'action': action,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      print('구독 상태 변경 오류: $e');
      return false;
    }
  }

  /// 관리자 로그 조회 (실데이터)
  static Future<List<SystemLog>> getAdminLogs() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['adminLogs'] != null) {
        final logsData = systemInfo['adminLogs'] as List;
        return logsData.map((log) => SystemLog(
          timestamp: DateTime.parse(log['timestamp']),
          level: log['success'] == true ? 'SUCCESS' : 'INFO',
          message: '관리자 로그인',
          details: '${log['username']} - ${log['ip']}',
        )).toList();
      }
    } catch (e) {
      print('관리자 로그 조회 실패: $e');
    }

    return [];
  }

  /// 자동 결제 로그 조회 (실데이터)
  static Future<List<SystemLog>> getAutoPaymentLogs() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['autoPaymentLogs'] != null) {
        final logsData = systemInfo['autoPaymentLogs'] as List;
        return logsData.map((log) => SystemLog(
          timestamp: DateTime.parse(log['timestamp']),
          level: log['level'] ?? 'INFO',
          message: log['message'] ?? '자동 결제 처리',
          details: '처리: ${log['totalProcessed']}건, 성공: ${log['successCount']}건, 실패: ${log['failureCount']}건',
        )).toList();
      }
    } catch (e) {
      print('자동 결제 로그 조회 실패: $e');
    }

    return [];
  }

  /// 접속 로그 조회 (실데이터)
  static Future<List<SystemLog>> getAccessLogs() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['accessLogs'] != null) {
        final logsData = systemInfo['accessLogs'] as List;
        return logsData.map((log) => SystemLog(
          timestamp: DateTime.parse(log['timestamp']),
          level: 'INFO',
          message: '사용자 접속',
          details: '${log['userId'] ?? 'Unknown'} - ${log['ip'] ?? 'Unknown IP'}',
        )).toList();
      }
    } catch (e) {
      print('접속 로그 조회 실패: $e');
    }

    return [];
  }

  /// 시스템 로그 조회 (하위 호환성을 위해 유지)
  static Future<List<SystemLog>> getSystemLogs() async {
    return await getAdminLogs();
  }

  /// 시스템 통계 조회 (실데이터)
  static Future<SystemStats> getSystemStats() async {
    try {
      final systemInfo = await getSystemInfo();
      if (systemInfo != null && systemInfo['systemStats'] != null) {
        final stats = systemInfo['systemStats'];
        return SystemStats(
          totalFunctionCalls: stats['totalFunctionCalls'] ?? '-',
          successRate: stats['successRate'] ?? '-',
          lastExecution: stats['lastExecution'] ?? '-',
        );
      }
    } catch (e) {
      print('실데이터 통계 조회 실패, 기본 데이터 반환: $e');
    }

    // 실데이터 조회 실패 시 기본 데이터 반환
    return SystemStats(
      totalFunctionCalls: '-',
      successRate: '-',
      lastExecution: '-',
    );
  }

  /// 접속 로그 기록
  static Future<void> logAccess(String userId, String? ip) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/logAccess'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'userId': userId,
          'ip': ip ?? 'Unknown',
          'timestamp': DateTime.now().toIso8601String(),
          'userAgent': 'Flutter App',
        }),
      );

      if (response.statusCode != 200) {
        print('접속 로그 기록 실패: ${response.statusCode}');
      }
    } catch (e) {
      print('접속 로그 기록 오류: $e');
    }
  }
}
