import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/seller.dart';
import '../../providers/seller_provider.dart';

import '../../utils/toast_utils.dart';
import '../../providers/nickname_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../providers/permission_provider.dart';
import '../../models/event_permission.dart';
import '../../widgets/confirmation_dialog.dart';
import '../../widgets/app_bar_styles.dart';
import '../../utils/subscription_utils.dart';
import '../subscription/subscription_plans_screen.dart';

class SellerManagementScreen extends ConsumerStatefulWidget {
  const SellerManagementScreen({super.key});

  @override
  ConsumerState<SellerManagementScreen> createState() =>
      _SellerManagementScreenState();
}

class _SellerManagementScreenState extends ConsumerState<SellerManagementScreen>
    with RestorationMixin {
  final TextEditingController _sellerNameController = TextEditingController();

  @override
  String? get restorationId => 'seller_management_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    // 화면 진입 시 한 번만 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadInitialData();
      }
    });
  }

  /// 초기 데이터 로드 (한 번만 실행)
  Future<void> _loadInitialData() async {
    if (!mounted) return;

    try {
      await ref.read(sellerNotifierProvider.notifier).loadSellers();
    } catch (e) {
      // 오류 발생 시 무시 (이미 Provider에서 처리됨)
    }
  }

  @override
  void dispose() {
    _sellerNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sellerAsync = ref.watch(sellerNotifierProvider);

    // 판매자별 관리 기능 접근 권한 확인
    return Consumer(
      builder: (context, ref, child) {
        return FutureBuilder<bool>(
          future: SubscriptionUtils.checkFeatureAccess(
            ref: ref,
            context: context,
            featureName: 'sellerManagement',
            showDialog: false,
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Scaffold(
                appBar: AppBar(
                  title: Builder(builder: (ctx)=> Text('판매자 관리', style: AppBarStyles.of(ctx))),
                  centerTitle: true,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
                body: const Center(child: CircularProgressIndicator()),
              );
            }

            final hasAccess = snapshot.data ?? false;

            if (!hasAccess) {
              return Scaffold(
                appBar: AppBar(
                  title: Builder(builder: (ctx)=> Text('판매자 관리', style: AppBarStyles.of(ctx))),
                  centerTitle: true,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
                body: SafeArea(
                  child: _buildRestrictedContent(),
                ),
              );
            }

            return Scaffold(
              appBar: AppBar(
                title: Builder(builder: (ctx)=> Text('판매자 관리', style: AppBarStyles.of(ctx))),
                centerTitle: true,
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
              body: SafeArea(
                child: _buildBody(sellerAsync),
              ),
              floatingActionButton: FloatingActionButton(
                onPressed: _navigateToAddSellerScreen,
                child: const Icon(Icons.add),
                tooltip: '판매자 추가',
              ),
            );
          },
        );
      },
    );
  }

  /// 기능 제한 안내 화면
  Widget _buildRestrictedContent() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.star,
              size: 80,
              color: Colors.amber,
            ),
            SizedBox(height: 24),
            Text(
              '프로 플랜 필요',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Text(
              '판매자별 관리 기능은 프로 플랜에서만 사용할 수 있습니다.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionPlansScreen(),
                  ),
                );
              },
              icon: Icon(Icons.upgrade),
              label: Text('프로 플랜으로 업그레이드'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(SellerState sellerAsync) {
    if (sellerAsync.isLoading) {
      return _buildLoadingState();
    } else if (sellerAsync.hasError) {
      return _buildErrorState(sellerAsync.errorMessage ?? 'Unknown error');
    } else {
      return _buildContent(sellerAsync);
    }
  }

  // 판매자 추가 화면으로 이동 (예시)
  Future<void> _navigateToAddSellerScreen() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => _buildAddSellerDialog(),
    );
    if (result == true) {
      ref.read(sellerNotifierProvider.notifier).loadSellers();
    }
  }

  // 판매자 추가 다이얼로그
  Widget _buildAddSellerDialog() {
    return AlertDialog(
      title: const Text('판매자 추가'),
      content: TextField(
        controller: _sellerNameController,
        decoration: const InputDecoration(hintText: '판매자 이름 입력'),
        autofocus: true,
        textInputAction: TextInputAction.done,
        onSubmitted: (_) => _onAddSellerDialogSubmit(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('취소'),
        ),
        ElevatedButton(
          onPressed: _onAddSellerDialogSubmit,
          child: const Text('추가'),
        ),
      ],
    );
  }

  Future<void> _onAddSellerDialogSubmit() async {
    final sellerName = _sellerNameController.text.trim();
    if (sellerName.isEmpty) {
      _showMessage('판매자 이름을 입력해주세요.');
      return;
    }
    
    final sellerState = ref.read(sellerNotifierProvider);
    final nickname = ref.read(nicknameProvider);
    
    // 중복 검사
    final isDuplicate = sellerState.sellers.any(
      (seller) => seller.name.toLowerCase() == sellerName.toLowerCase(),
    );
    if (isDuplicate) {
      _showMessage('\'$sellerName\' 판매자는 이미 존재합니다.');
      return;
    }
    
    // 닉네임과 중복 검사
    if (nickname != null && sellerName.toLowerCase() == nickname.name.toLowerCase()) {
      _showMessage('\'$sellerName\'은 로그인된 사용자의 닉네임과 동일합니다. 다른 이름을 사용해주세요.');
      return;
    }
    
    // 현재 워크스페이스 확인
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      _showMessage('현재 선택된 행사가 없습니다. 행사를 선택해주세요.');
      return;
    }
    
    try {
      final newSeller = Seller.create(name: sellerName, eventId: currentWorkspace.id);
      await ref.read(sellerNotifierProvider.notifier).addSeller(newSeller);
      _sellerNameController.clear();
      _showMessage('\'$sellerName\' 판매자가 추가되었습니다.');
      Navigator.of(context).pop(true); // 반드시 true 반환
    } catch (e) {
      _showMessage('판매자 추가 중 오류가 발생했습니다: $e');
    }
  }

  /// 삭제 확인 다이얼로그
  Future<void> _showDeleteConfirmDialog(Seller seller) async {
    final confirmed = await ConfirmationDialog.showDelete(
      context: context,
      title: '판매자 삭제',
      message: '\'${seller.name}\' 판매자를 삭제하시겠습니까?',
      onConfirm: () async {
        try {
          await ref.read(sellerNotifierProvider.notifier).deleteSeller(seller.id!);
          _showMessage('\'${seller.name}\' 판매자가 삭제되었습니다.');
        } catch (e) {
          _showMessage('판매자 삭제 중 오류가 발생했습니다.');
        }
      },
    );
    if (confirmed == true) {
      ref.read(sellerNotifierProvider.notifier).loadSellers();
    }
  }

  /// 사용자 추방 확인 다이얼로그
  Future<void> _showKickUserDialog(Seller seller, EventPermission permission) async {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('사용자 추방'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${seller.name} 사용자를 이 행사에서 추방하시겠습니까?'),
            const SizedBox(height: 8),
            const Text(
              '추방된 사용자는 더 이상 이 행사에 접근할 수 없습니다.',
              style: TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _kickUser(seller, permission);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('추방'),
          ),
        ],
      ),
    );
  }

  /// 사용자 추방 처리
  Future<void> _kickUser(Seller seller, EventPermission permission) async {
    try {
      // 권한 제거
      await ref.read(permissionNotifierProvider.notifier)
          .revokePermission(seller.eventId, permission.userId);

      // 판매자 목록 새로고침
      await ref.read(sellerNotifierProvider.notifier).loadSellers();

      _showMessage('${seller.name} 사용자가 추방되었습니다');
    } catch (e) {
      _showMessage('사용자 추방 실패: ${e.toString()}');
    }
  }

  /// 메시지 표시
  void _showMessage(String message) {
    ToastUtils.showMessage(context, message);
  }

  /// 판매자 수정 다이얼로그
  Future<void> _showEditSellerDialog(Seller seller) async {
    final controller = TextEditingController(text: seller.name);

    try {
      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text(
            '판매자 이름 수정',
            style: TextStyle(fontFamily: 'Pretendard'),
          ),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(hintText: '새 판매자 이름 입력'),
            autofocus: true,
            textInputAction: TextInputAction.done,
            style: const TextStyle(fontFamily: 'Pretendard'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                '취소',
                style: TextStyle(fontFamily: 'Pretendard'),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(controller.text.trim()),
              child: const Text(
                '수정',
                style: TextStyle(fontFamily: 'Pretendard'),
              ),
            ),
          ],
        ),
      );

      if (result != null && result.isNotEmpty && result != seller.name) {
        try {
          await ref.read(sellerNotifierProvider.notifier).updateSeller(
            seller.copyWith(name: result),
          );
          _showMessage('판매자 이름이 수정되었습니다.');
        } catch (e) {
          _showMessage('판매자 이름 수정 중 오류가 발생했습니다.');
        }
      }
    } finally {
      // 다이얼로그가 완전히 닫힌 후에 컨트롤러를 안전하게 dispose
      controller.dispose();
    }
  }

  Widget _buildContent(SellerState sellerAsync) {
    final nickname = ref.watch(nicknameProvider);
    return ListView.builder(
      itemCount: sellerAsync.sellers.length,
      itemExtent: 72.0, // 고정 높이로 스크롤 성능 최적화 (ListTile + subtitle)
      itemBuilder: (context, index) {
        final seller = sellerAsync.sellers[index];
        // 닉네임과 이름이 같고 대표 판매자인 경우 닉네임 기반 판매자로 판단
        final isLinkedToNickname = nickname != null && 
                                 seller.name == nickname.name && 
                                 seller.isDefault;
        return FutureBuilder<List<EventPermission>>(
          future: ref.read(eventPermissionRepositoryProvider).getInvitedUsers(seller.eventId),
          builder: (context, snapshot) {
            final invitedUsers = snapshot.data ?? [];
            final isInvitedUser = invitedUsers.any((permission) => permission.userNickname == seller.name);

            String? subtitleText;
            Color? subtitleColor;
            bool canModify = true;

            if (isLinkedToNickname) {
              subtitleText = '로그인된 판매자  ( 수정/삭제 불가 )';
              subtitleColor = Theme.of(context).colorScheme.primary;
              canModify = false;
            } else if (isInvitedUser) {
              subtitleText = '초대된 사용자  ( 수정/삭제 불가 )';
              subtitleColor = Colors.orange;
              canModify = false;
            }

            return ListTile(
              title: Text(seller.name),
              subtitle: subtitleText != null
                  ? Text(subtitleText, style: TextStyle(color: subtitleColor))
                  : null,
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: canModify ? () => _showEditSellerDialog(seller) : null,
                    tooltip: !canModify
                        ? (isLinkedToNickname
                            ? '닉네임과 연결된 판매자는 수정할 수 없습니다'
                            : '초대된 사용자는 수정할 수 없습니다')
                        : '수정',
                  ),
                  if (!isInvitedUser) // 초대된 사용자가 아닌 경우에만 삭제 버튼 표시
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: canModify ? () => _showDeleteConfirmDialog(seller) : null,
                      tooltip: !canModify
                          ? '닉네임과 연결된 판매자는 삭제할 수 없습니다'
                          : '삭제',
                    )
                  else // 초대된 사용자인 경우 추방 버튼 표시
                    FutureBuilder<bool>(
                      future: ref.read(permissionNotifierProvider.notifier).isEventOwner(seller.eventId),
                      builder: (context, ownerSnapshot) {
                        final isOwner = ownerSnapshot.data ?? false;
                        if (isOwner) {
                          return IconButton(
                            icon: const Icon(Icons.person_remove, color: Colors.red),
                            onPressed: () => _showKickUserDialog(seller, invitedUsers.firstWhere((p) => p.userNickname == seller.name)),
                            tooltip: '사용자 추방',
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Text(
        '판매자 목록을 불러오는 중 오류가 발생했습니다.\n$error',
        textAlign: TextAlign.center,
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.red),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }
}


