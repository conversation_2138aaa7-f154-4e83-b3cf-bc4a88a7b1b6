import 'package:flutter/material.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../utils/app_colors.dart';
import '../../utils/logger_utils.dart';

/// 전화번호 인증 화면
class PhoneVerificationScreen extends StatefulWidget {
  const PhoneVerificationScreen({super.key});

  @override
  State<PhoneVerificationScreen> createState() => _PhoneVerificationScreenState();
}

class _PhoneVerificationScreenState extends State<PhoneVerificationScreen> {
  static const String _tag = 'PhoneVerificationScreen';
  
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final FocusNode _phoneFocus = FocusNode();
  final FocusNode _codeFocus = FocusNode();

  bool _isLoading = false;
  bool _isCodeSent = false;
  bool _isVerified = false;
  String? _error;
  String? _currentPhoneNumber;
  int _remainingTime = 0;
  bool _isCaptchaVerified = false;

  // 웹뷰 관련
  WebViewController? _webViewController;
  
  @override
  void initState() {
    super.initState();
    _loadCurrentPhoneNumber();
    _initializeWebView();
  }

  /// 웹뷰 초기화
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            LoggerUtils.logInfo('전화번호 인증 웹뷰 로드 완료: $url');
          },
          onWebResourceError: (WebResourceError error) {
            LoggerUtils.logError('웹뷰 로드 오류: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'phoneVerificationSuccess',
        onMessageReceived: (JavaScriptMessage message) {
          LoggerUtils.logInfo('전화번호 인증 성공: ${message.message}');
          _handleVerificationSuccess(message.message);
        },
      )
      ..loadRequest(Uri.parse('https://parabara-1a504.web.app/phone-verification.html'));
  }

  /// 전화번호 인증 성공 처리
  void _handleVerificationSuccess(String message) {
    try {
      // JSON 파싱 (예: {"phoneNumber": "010-1234-5678", "verified": true})
      // 실제로는 더 복잡한 파싱이 필요할 수 있음
      LoggerUtils.logInfo('전화번호 인증 완료: $message');

      // 성공 처리 후 화면 닫기
      if (mounted) {
        Navigator.of(context).pop(true); // 성공 결과 반환
      }
    } catch (e) {
      LoggerUtils.logError('전화번호 인증 결과 처리 오류', error: e);
    }
  }
  
  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _phoneFocus.dispose();
    _codeFocus.dispose();
    super.dispose();
  }
  
  /// 현재 사용자의 전화번호 정보 로드
  Future<void> _loadCurrentPhoneNumber() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();
        
        if (doc.exists && doc.data() != null) {
          final data = doc.data()!;
          final phone = data['phone'] as String?;
          final phoneVerified = data['phoneVerified'] as bool? ?? false;
          
          if (mounted) {
            setState(() {
              _currentPhoneNumber = phone;
              _isVerified = phoneVerified;
              if (phone != null) {
                _phoneController.text = phone;
              }
            });
          }
        }
      }
    } catch (e) {
      LoggerUtils.logError('전화번호 정보 로드 실패', tag: _tag, error: e);
    }
  }
  
  /// SMS 인증번호 발송
  Future<void> _sendVerificationCode() async {
    if (_phoneController.text.trim().isEmpty) {
      setState(() {
        _error = '전화번호를 입력해주세요.';
      });
      return;
    }
    
    final phoneNumber = _phoneController.text.trim().replaceAll('-', '');
    if (!RegExp(r'^01[0-9]{8,9}$').hasMatch(phoneNumber)) {
      setState(() {
        _error = '올바른 전화번호를 입력해주세요.';
      });
      return;
    }

    // 캡챠 인증 확인
    if (!_isCaptchaVerified) {
      await _showCaptchaDialog();
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });
    
    try {
      final functions = FirebaseFunctions.instance;
      final callable = functions.httpsCallable('sendSmsVerification');
      
      final result = await callable.call({
        'phoneNumber': _formatPhoneNumber(phoneNumber),
      });
      
      if (result.data['success'] == true) {
        setState(() {
          _isCodeSent = true;
          _remainingTime = 300; // 5분
        });
        
        _startTimer();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('인증번호가 발송되었습니다.'),
              backgroundColor: Colors.green,
            ),
          );
        }
        
        // 코드 입력 필드로 포커스 이동
        _codeFocus.requestFocus();
      }
    } catch (e) {
      LoggerUtils.logError('SMS 발송 실패', tag: _tag, error: e);
      setState(() {
        _error = e.toString().replaceAll('Exception: ', '');
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 인증번호 확인
  Future<void> _verifyCode() async {
    if (_codeController.text.trim().isEmpty) {
      setState(() {
        _error = '인증번호를 입력해주세요.';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _error = null;
    });
    
    try {
      final functions = FirebaseFunctions.instance;
      final callable = functions.httpsCallable('verifySmsCode');
      
      final phoneNumber = _phoneController.text.trim().replaceAll('-', '');
      final result = await callable.call({
        'phoneNumber': _formatPhoneNumber(phoneNumber),
        'code': _codeController.text.trim(),
      });
      
      if (result.data['success'] == true) {
        setState(() {
          _isVerified = true;
          _currentPhoneNumber = _formatPhoneNumber(phoneNumber);
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('전화번호 인증이 완료되었습니다.'),
              backgroundColor: Colors.green,
            ),
          );
          
          // 2초 후 이전 화면으로 돌아가기
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              Navigator.of(context).pop(true); // 인증 성공 결과 전달
            }
          });
        }
      }
    } catch (e) {
      LoggerUtils.logError('인증번호 확인 실패', tag: _tag, error: e);
      setState(() {
        _error = e.toString().replaceAll('Exception: ', '');
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 타이머 시작
  void _startTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _remainingTime > 0) {
        setState(() {
          _remainingTime--;
        });
        _startTimer();
      }
    });
  }
  
  /// 캡챠 인증 다이얼로그 표시
  Future<void> _showCaptchaDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('보안 인증'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('SMS 발송을 위해 보안 인증을 완료해주세요.'),
              const SizedBox(height: 16),
              _buildRecaptchaWidget(),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('취소'),
            ),
          ],
        );
      },
    );
  }

  /// reCAPTCHA v2 위젯 구성 (임시 구현)
  Widget _buildRecaptchaWidget() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.security, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'reCAPTCHA 보안 인증',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // 임시 캡챠 인증 (실제로는 reCAPTCHA v2 구현 필요)
                LoggerUtils.logInfo('임시 reCAPTCHA 인증 완료');
                setState(() {
                  _isCaptchaVerified = true;
                });
                Navigator.of(context).pop();
                _sendVerificationCode(); // 캡챠 인증 후 SMS 발송
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primarySeed,
                foregroundColor: Colors.white,
              ),
              child: const Text('보안 인증 완료'),
            ),
          ],
        ),
      ),
    );
  }

  /// 전화번호 포맷팅 (010-1234-5678)
  String _formatPhoneNumber(String phoneNumber) {
    final cleaned = phoneNumber.replaceAll('-', '');
    if (cleaned.length == 11) {
      return '${cleaned.substring(0, 3)}-${cleaned.substring(3, 7)}-${cleaned.substring(7)}';
    } else if (cleaned.length == 10) {
      return '${cleaned.substring(0, 3)}-${cleaned.substring(3, 6)}-${cleaned.substring(6)}';
    }
    return phoneNumber;
  }
  
  /// 남은 시간 포맷팅 (mm:ss)
  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('전화번호 인증'),
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: WebViewWidget(
          controller: _webViewController!,
        ),
      ),
    );
  }


}
