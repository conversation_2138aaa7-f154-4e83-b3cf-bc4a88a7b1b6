import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;

import '../../providers/product_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../providers/data_sync_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../models/product.dart';
import '../../models/seller.dart';
import '../../widgets/image_crop_widget.dart';
import '../../widgets/product_image.dart';
import '../../utils/toast_utils.dart';
import '../../utils/logger_utils.dart';
import '../../utils/error_utils.dart';
import '../../utils/image_utils.dart';
import '../../utils/subscription_utils.dart';
import '../../utils/app_colors.dart';
import '../../utils/permission_utils.dart';
import '../../widgets/app_bar_styles.dart';

class RegisterProductScreen extends ConsumerStatefulWidget {
  final Product? product;
  final int? productId;
  final bool isEditing;
  final int? initialCategoryId; // 초기 카테고리 ID 추가
  final bool isCopyMode; // 복사 모드 추가

  const RegisterProductScreen({
    super.key,
    this.product,
    this.productId,
    this.isEditing = false,
    this.initialCategoryId, // 초기 카테고리 ID 매개변수 추가
    this.isCopyMode = false, // 복사 모드 기본값
  });

  @override
  ConsumerState<RegisterProductScreen> createState() =>
      _RegisterProductScreenState();
}

class _RegisterProductScreenState extends ConsumerState<RegisterProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _sellerNameController = TextEditingController();

  // FocusNode 추가
  final FocusNode _nameFocusNode = FocusNode();
  final FocusNode _priceFocusNode = FocusNode();
  final FocusNode _quantityFocusNode = FocusNode();

  Product? _currentProduct;
  XFile? _selectedImage;
  String? _selectedSellerName;
  int? _selectedCategoryId; // 선택된 카테고리 ID
  bool _categoryInitialized = false; // 카테고리 초기화 완료 플래그
  bool _isLoading = false;
  String? _croppedImagePath;
  String? _originalPaddedImagePath; // 원본 패딩 이미지 경로 저장

  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: 'RegisterProductScreen');

    // 기존 상품이 있는 경우 데이터 로드
    if (widget.product != null) {
      if (widget.isCopyMode) {
        // 복사 모드: 기존 상품 정보를 복사하되 ID는 null로 설정 (새 상품 생성)
        _currentProduct = null; // 새 상품으로 처리
        _nameController.text = widget.product!.name;
        _priceController.text = widget.product!.price.toString();
        _quantityController.text = widget.product!.quantity.toString();
        _selectedSellerName = widget.product!.sellerName;
        _sellerNameController.text = widget.product!.sellerName ?? '';
        // 복사 모드에서는 카테고리를 초기화하지 않음 (사용자가 선택하도록)
        _categoryInitialized = false;

        // 기존 상품의 이미지 정보 복사
        if (widget.product!.imagePath != null && widget.product!.imagePath!.isNotEmpty) {
          _croppedImagePath = widget.product!.imagePath;
        }
      } else {
        // 수정 모드: 기존 상품 정보 로드
        _currentProduct = widget.product;
        _nameController.text = widget.product!.name;
        _priceController.text = widget.product!.price.toString();
        _quantityController.text = widget.product!.quantity.toString();
        _selectedSellerName = widget.product!.sellerName;
        _sellerNameController.text = widget.product!.sellerName ?? '';
        _selectedCategoryId = widget.product!.categoryId;
        _categoryInitialized = true;

        // 기존 상품의 이미지 정보 설정
        if (widget.product!.imagePath != null && widget.product!.imagePath!.isNotEmpty) {
          _croppedImagePath = widget.product!.imagePath;
        }
      }
    } else {
      // 새 상품 등록 시
      if (widget.initialCategoryId != null) {
        // 초기 카테고리 ID가 전달된 경우
        _selectedCategoryId = widget.initialCategoryId;
        _categoryInitialized = true;
        LoggerUtils.logInfo('초기 카테고리 ID 설정: ${widget.initialCategoryId}', tag: 'RegisterProductScreen');
      } else {
        // 카테고리 로드 후 설정
        _categoryInitialized = false;
      }

      // 새 상품 등록 시 대표 판매자를 기본값으로 설정
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final sellerState = ref.read(sellerNotifierProvider);
        if (sellerState.defaultSeller != null) {
          setState(() {
            _selectedSellerName = sellerState.defaultSeller!.name;
          });
        }
      });
    }
    
    // 카테고리 목록 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(categoryNotifierProvider.notifier).loadCategories();
    });

    LoggerUtils.methodEnd('initState', tag: 'RegisterProductScreen');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    _sellerNameController.dispose();
    
    // FocusNode 해제
    _nameFocusNode.dispose();
    _priceFocusNode.dispose();
    _quantityFocusNode.dispose();
    
    super.dispose();
  }

  Future<void> _selectImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        // 1. 흰색 캔버스+중앙 배치 적용
        final originalBytes = await image.readAsBytes();
        final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
        final tempDir = await getTemporaryDirectory();
        final paddedPath = path.join(tempDir.path, 'padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
        final paddedFile = await File(paddedPath).writeAsBytes(paddedBytes);

        // 원본 패딩 이미지 경로 저장 (재크롭을 위해)
        _originalPaddedImagePath = paddedFile.path;

        // 2. 크롭 다이얼로그(라운드 사각형, 1:1, 오버레이/로딩/원본노출방지)
        final croppedFile = await ImageCropUtils.cropImage(
          context: context,
          imagePath: paddedFile.path,
          shape: CropShape.roundedSquare,
          aspectRatio: 1.0,
        );

        if (croppedFile != null) {
          setState(() {
            _croppedImagePath = croppedFile.path;
            _selectedImage = null;
          });
        } else {
          // 크롭을 취소한 경우 이미지를 표시하지 않음
          setState(() {
            _selectedImage = null;
            _croppedImagePath = null;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '이미지 선택 중 오류가 발생했습니다: $e',
        );
      }
    }
  }

  /// 기존 이미지를 다시 크롭합니다
  Future<void> _recropImage() async {
    if (_originalPaddedImagePath == null) return;

    try {
      final croppedFile = await ImageCropUtils.cropImage(
        context: context,
        imagePath: _originalPaddedImagePath!,
        shape: CropShape.roundedSquare,
        aspectRatio: 1.0,
      );

      if (croppedFile != null) {
        setState(() {
          _croppedImagePath = croppedFile.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '이미지 크롭 중 오류가 발생했습니다: $e',
        );
      }
    }
  }

  Future<String?> _saveImageToInternalStorage(XFile image) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final productImagesDir = Directory(
        path.join(appDir.path, 'product_images'),
      );

      if (!await productImagesDir.exists()) {
        await productImagesDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'SOGOM_IMG_$timestamp.jpg';
      final savedImage = File(path.join(productImagesDir.path, fileName));

      // 이미지 바이트 읽기
      final imageBytes = await image.readAsBytes();
      final img.Image? imgDecoded = img.decodeImage(imageBytes);
      final img.Image imgResized = img.copyResize(imgDecoded!, width: 400, height: 400);
      final jpgBytes = img.encodeJpg(imgResized, quality: 80);
      await savedImage.writeAsBytes(jpgBytes);
      return savedImage.path;
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '이미지 저장 중 오류가 발생했습니다: $e',
        );
      }
      return null;
    }
  }

  Future<void> _saveProduct() async {
    if (!mounted) return;

    LoggerUtils.methodStart('_saveProduct', tag: 'RegisterProductScreen');

    // 권한 체크
    final hasPermission = await PermissionUtils.checkModifyPermission(context, ref);
    if (!hasPermission) {
      return;
    }

    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 현재 선택된 행사 확인
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      ToastUtils.showError(context, '현재 선택된 행사가 없습니다. 행사를 선택해주세요.');
      return;
    }

    // EventWorkspace를 Event로 변환
    final currentEvent = EventWorkspaceUtils.workspaceToEvent(currentWorkspace);
    if (currentEvent == null) {
      ToastUtils.showError(context, '행사 정보를 불러올 수 없습니다.');
      return;
    }

    // 상품명 중복 검증 (같은 카테고리 내에서)
    if (_selectedCategoryId != null) {
      final isDuplicate = await _checkProductNameDuplicate(
        _nameController.text.trim(),
        _selectedCategoryId!,
        currentWorkspace.id,
        excludeProductId: widget.isEditing && !widget.isCopyMode ? _currentProduct?.id : null,
      );

      if (isDuplicate) {
        ToastUtils.showError(context, '같은 카테고리에 동일한 이름의 상품이 이미 존재합니다.');
        return;
      }
    }

    // 새 상품 등록 시 구독 제한 확인
    if (_currentProduct == null) {
      final currentProducts = ref.read(productNotifierProvider).products;
      final currentProductCount = currentProducts.where((p) => p.eventId == currentWorkspace.id).length;

      final canCreate = await SubscriptionUtils.checkProductCreationLimit(
        ref: ref,
        context: context,
        currentProductCount: currentProductCount,
      );

      if (!canCreate) {
        return;
      }
    }

    // 수정 모드에서 판매자 변경 감지
    bool sellerChanged = false;
    if (_currentProduct != null &&
        _currentProduct!.sellerName != _selectedSellerName &&
        _selectedSellerName != null) {
      sellerChanged = true;

      // 판매자 변경 경고 다이얼로그 표시
      final shouldProceed = await _showSellerChangeWarning();
      if (!shouldProceed) {
        return;
      }
    }

    // 수정 모드에서 상품명 변경 감지
    bool nameChanged = false;
    if (_currentProduct != null &&
        _currentProduct!.name != _nameController.text) {
      nameChanged = true;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 이미지 저장 처리
      String? savedImagePath;
      if (_croppedImagePath != null) {
        // 크롭된 이미지가 있으면 그대로 사용
        savedImagePath = _croppedImagePath;
      } else if (_selectedImage != null) {
        savedImagePath = await _saveImageToInternalStorage(_selectedImage!);
      }

      // 카테고리 선택 검증
      if (_selectedCategoryId == null) {
        if (mounted) {
          ToastUtils.showError(context, '카테고리를 선택해주세요');
        }
        return;
      }

      // 상품명 검증 및 정리
      final productName = _nameController.text.trim();
      if (productName.isEmpty) {
        ToastUtils.showError(context, '상품명을 입력해주세요.');
        return;
      }

      final product = Product(
        id: (widget.isEditing && !widget.isCopyMode) ? _currentProduct?.id : null, // 수정 모드에서만 기존 ID 유지, 새 상품 등록과 복사 모드에서는 명시적으로 null
        name: productName, // trim() 처리된 상품명 사용
        price: int.parse(_priceController.text.trim()),
        quantity: int.parse(_quantityController.text.trim()),
        sellerName: _selectedSellerName,
        imagePath: savedImagePath ?? _currentProduct?.imagePath, // 기존 이미지 유지
        eventId: _currentProduct?.eventId ?? currentEvent.id ?? currentWorkspace.id, // 현재 행사 ID 설정
        categoryId: _selectedCategoryId!, // null 체크 후 사용
      );

      // 상세한 저장 로그
      LoggerUtils.logInfo('=== 상품 저장 시작 ===', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('상품명: ${product.name}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('선택된 카테고리 ID: ${product.categoryId}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('상품 ID: ${product.id}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('수정 모드: ${_currentProduct != null}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('위젯 수정 모드: ${widget.isEditing}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('위젯 복사 모드: ${widget.isCopyMode}', tag: 'RegisterProductScreen');
      LoggerUtils.logInfo('ID 설정 조건: ${(widget.isEditing && !widget.isCopyMode) ? "기존 ID 유지" : "새 ID 생성(null)"}', tag: 'RegisterProductScreen');
      
      // 카테고리 정보 추가 로그
      final categoriesAsync = ref.read(categoryNotifierProvider);
      if (categoriesAsync.hasValue) {
        final allCategories = categoriesAsync.value!;
        final selectedCategory = allCategories.firstWhere(
          (cat) => cat.id == product.categoryId,
          orElse: () => throw Exception('카테고리를 찾을 수 없습니다: ${product.categoryId}')
        );
        LoggerUtils.logInfo('저장될 카테고리: ${selectedCategory.name} (ID: ${selectedCategory.id})', tag: 'RegisterProductScreen');
      }

      await ErrorUtils.wrapError(
        context,
        () async {
          if (mounted) {
            if (_currentProduct == null) {
              // 상품 추가 시 다중 등록과 동일한 패턴: 로컬 저장 → 백그라운드 Firebase 업로드
              await ref.read(productNotifierProvider.notifier).addProduct(product, skipFirebase: true);
              
              // 즉시 UI 갱신 (다중 등록과 동일)
              await ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
              
              // 백그라운드에서 Firebase 업로드 스케줄링
              _scheduleBackgroundFirebaseUpload(product);
              
            } else {
              // 판매자 변경 또는 상품명 변경 시 연관 기록 업데이트 포함
              await ref.read(productNotifierProvider.notifier).updateProduct(
                product,
                updateRelatedSalesLogs: sellerChanged || nameChanged
              );

              // 간단하고 명확한 완료 메시지 표시
              if (mounted) {
                ToastUtils.showToast(
                  context,
                  _currentProduct == null
                      ? '${product.name} 상품이 등록되었습니다.'
                      : '${product.name} 상품이 수정되었습니다.',
                  duration: ToastUtils.shortDuration,
                );
              }
            }
          }

          if (mounted) {
            Navigator.of(context).pop(true);
          }
        },
        errorMessage: _currentProduct == null
            ? '상품을 등록하는 중 오류가 발생했습니다'
            : '상품을 수정하는 중 오류가 발생했습니다',
        type: ErrorType.database,
        tag: 'RegisterProductScreen',
      );
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          '상품 저장 중 오류가 발생했습니다: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }

    LoggerUtils.methodEnd('_saveProduct', tag: 'RegisterProductScreen');
  }

  /// 상품명 중복 검증
  Future<bool> _checkProductNameDuplicate(
    String productName,
    int categoryId,
    int eventId, {
    int? excludeProductId,
  }) async {
    try {
      final products = ref.read(productNotifierProvider).products;

      // 같은 카테고리 내에서 동일한 상품명이 있는지 확인
      final duplicateProduct = products.firstWhere(
        (product) =>
          product.categoryId == categoryId &&
          product.eventId == eventId &&
          product.name.trim().toLowerCase() == productName.toLowerCase() &&
          (excludeProductId == null || product.id != excludeProductId),
        orElse: () => Product(
          name: '',
          price: 0,
          quantity: 0,
          eventId: eventId,
          categoryId: categoryId,
        ),
      );

      return duplicateProduct.name.isNotEmpty;
    } catch (e) {
      LoggerUtils.logError('상품명 중복 검증 실패', error: e, tag: 'RegisterProductScreen');
      return false; // 검증 실패 시 중복 없음으로 처리
    }
  }

  /// 판매자 변경 시 경고 다이얼로그를 표시합니다.
  Future<bool> _showSellerChangeWarning() async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('판매자 변경 확인'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('상품 "${_currentProduct!.name}"의 판매자가 변경됩니다.'),
              const SizedBox(height: 8),
              const Text(
                '이 변경은 기존 판매 기록과 통계에도 반영됩니다.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                '• 기존 판매 기록의 판매자 정보가 업데이트됩니다\n'
                '• 판매 통계가 새로운 판매자 기준으로 재계산됩니다\n'
                '• 이 작업은 되돌릴 수 없습니다',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('취소'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('계속 진행'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  Widget _buildImageSection() {
    String? imagePath;
    if (_croppedImagePath != null) {
      imagePath = _croppedImagePath;
    } else if (_selectedImage != null) {
      imagePath = _selectedImage!.path;
    } else if (_currentProduct?.imagePath != null && _currentProduct!.imagePath!.isNotEmpty) {
      imagePath = _currentProduct!.imagePath;
    }

    return Column(
      children: [
        // 이미지 컨테이너 (재크롭 전용)
        GestureDetector(
          onTap: imagePath != null && _originalPaddedImagePath != null ? _recropImage : null,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
              border: imagePath != null && _originalPaddedImagePath != null
                  ? Border.all(color: AppColors.primarySeed.withValues(alpha: 0.5), width: 2)
                  : null,
            ),
            child: imagePath != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: AspectRatio(
                      aspectRatio: 1.0, // 정사각형 비율 유지
                      child: ProductImage(
                        imagePath: imagePath,
                        fit: BoxFit.cover, // 높은 품질의 크롭 이미지
                      ),
                    ),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_a_photo_outlined,
                        size: 48,
                        color: AppColors.primarySeed.withValues(alpha: 0.7),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '상품 사진 추가',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.primarySeed.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        const SizedBox(height: 12),

        // 사진 변경 버튼 (항상 표시)
        SizedBox(
          width: 200,
          child: ElevatedButton.icon(
            onPressed: _selectImage,
            icon: Icon(
              imagePath != null ? Icons.edit : Icons.add_a_photo,
              size: 18,
            ),
            label: Text(
              imagePath != null ? '사진 변경' : '사진 선택',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primarySeed.withValues(alpha: 0.1),
              foregroundColor: AppColors.primarySeed,
              elevation: 0,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: AppColors.primarySeed.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
          ),
        ),

        // 재크롭 안내 텍스트
        if (imagePath != null && _originalPaddedImagePath != null) ...[
          const SizedBox(height: 8),
          Text(
            '사진을 터치하여 크롭 영역을\n다시 지정할 수 있습니다.',
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
              fontFamily: 'Pretendard',
              fontSize: 11,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }





  /// 이미지와 기본 정보를 가로로 배치하는 위젯
  Widget _buildImageAndBasicInfoRow() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isWideScreen = screenWidth > 600; // 태블릿 이상에서만 가로 배치

    if (isWideScreen) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 왼쪽: 이미지 섹션
          _buildImageSection(),
          const SizedBox(width: 24),
          // 오른쪽: 기본 정보 필드들
          Expanded(child: _buildBasicInfoFields()),
        ],
      );
    } else {
      // 모바일에서는 세로 배치
      return Column(
        children: [
          Center(child: _buildImageSection()),
          const SizedBox(height: 24),
          _buildBasicInfoFields(),
        ],
      );
    }
  }

  /// 기본 정보 필드들 (상품명, 가격, 수량, 카테고리, 판매자)
  Widget _buildBasicInfoFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 상품명
        _buildModernTextField(
          controller: _nameController,
          label: '상품명',
          hint: '상품명을 입력하세요',
          icon: Icons.inventory_2_outlined,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '상품명을 입력해주세요';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 가격과 수량을 가로로 배치
        Row(
          children: [
            Expanded(
              child: _buildModernTextField(
                controller: _priceController,
                label: '가격',
                hint: '0',
                icon: Icons.attach_money,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '가격을 입력해주세요';
                  }
                  final price = int.tryParse(value.trim());
                  if (price == null || price < 0) {
                    return '올바른 가격을 입력해주세요';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildModernTextField(
                controller: _quantityController,
                label: '수량',
                hint: '0',
                icon: Icons.inventory,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '수량을 입력해주세요';
                  }
                  final quantity = int.tryParse(value.trim());
                  if (quantity == null || quantity < 0) {
                    return '올바른 수량을 입력해주세요';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // 카테고리 선택
        _buildModernDropdown(),
        const SizedBox(height: 16),

        // 판매자 선택
        _buildSellerDropdown(),
      ],
    );
  }



  /// 모던한 카테고리 드롭다운 빌더
  Widget _buildModernDropdown() {
    return Consumer(
      builder: (context, ref, child) {
        final categoriesAsync = ref.watch(categoryNotifierProvider);

        return categoriesAsync.when(
          loading: () => Container(
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.neutral30),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, _) => Container(
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.error),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text('카테고리 로드 실패'),
            ),
          ),
          data: (categories) {
            if (categories.isEmpty) {
              return Container(
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.error),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Center(
                  child: Text('카테고리가 없습니다'),
                ),
              );
            }

            // 카테고리 초기화 로직
            if (!_categoryInitialized && widget.product == null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() {
                    final sortedCategories = [...categories]
                      ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
                    _selectedCategoryId = sortedCategories.first.id;
                    _categoryInitialized = true;
                  });
                }
              });
            }

            return DropdownButtonFormField<int>(
              value: _selectedCategoryId,
              decoration: InputDecoration(
                labelText: '카테고리',
                prefixIcon: Icon(Icons.category_outlined, color: AppColors.primarySeed),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.neutral30),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.neutral30),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
                ),
                filled: true,
                fillColor: AppColors.surface,
              ),
              items: categories.map((category) {
                return DropdownMenuItem<int>(
                  value: category.id,
                  child: Text(category.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategoryId = value;
                });
              },
              validator: (value) {
                if (value == null) {
                  return '카테고리를 선택해주세요';
                }
                return null;
              },
            );
          },
        );
      },
    );
  }

  /// 모던한 판매자 드롭다운 빌더
  Widget _buildSellerDropdown() {
    return Consumer(
      builder: (context, ref, child) {
        final sellerAsync = ref.watch(sellerNotifierProvider);
        final sellers = sellerAsync.isLoading
            ? <Seller>[]
            : sellerAsync.hasError
                ? <Seller>[]
                : sellerAsync.sellers;

        return DropdownButtonFormField<String>(
          value: _selectedSellerName,
          decoration: InputDecoration(
            labelText: '판매자',
            prefixIcon: Icon(Icons.person_outline, color: AppColors.primarySeed),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.neutral30),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.neutral30),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          items: sellers.map((seller) {
            final isDefault = seller.isDefault;
            return DropdownMenuItem<String>(
              value: seller.name,
              child: Row(
                children: [
                  if (isDefault)
                    const Icon(Icons.star, color: Colors.amber, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    seller.name,
                    style: TextStyle(
                      fontWeight: isDefault ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  if (isDefault)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.amber.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        '대표',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.amber,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedSellerName = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '판매자를 선택해주세요';
            }
            return null;
          },
        );
      },
    );
  }

  /// 모던한 텍스트 필드 위젯 빌더
  Widget _buildModernTextField({
    required TextEditingController controller,
    FocusNode? focusNode,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    Function(String)? onFieldSubmitted,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      focusNode: focusNode,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      onFieldSubmitted: onFieldSubmitted,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: AppColors.primarySeed),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.neutral30),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.neutral30),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primarySeed, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
        filled: true,
        fillColor: AppColors.surface,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      validator: validator,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.surface,
      appBar: AppBar(
        title: Builder(builder: (ctx)=> Text(widget.isEditing ? '상품 수정' : '상품 등록', style: AppBarStyles.of(ctx).copyWith(color: AppColors.onSurface))),
        backgroundColor: AppColors.surface,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor: AppColors.elevation1,
        actions: [
          IconButton(
            onPressed: _isLoading ? null : _saveProduct,
            icon: Icon(
              Icons.check,
              color: _isLoading ? Colors.grey : AppColors.primarySeed,
            ),
            tooltip: '저장',
          ),
        ],
      ),
      body: SafeArea(
        child: _isLoading
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: AppColors.primarySeed,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '상품 정보를 처리하고 있습니다...',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              )
            : Column(
                children: [
                  // 메인 콘텐츠 영역
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(20.0),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // 이미지와 기본 정보를 가로로 배치
                            _buildImageAndBasicInfoRow(),
                            const SizedBox(height: 100), // 하단 버튼 공간 확보
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 하단 고정 저장 버튼
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveProduct,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primarySeed,
                          foregroundColor: AppColors.onPrimary,
                          padding: const EdgeInsets.symmetric(vertical: 20),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 2,
                          shadowColor: AppColors.primarySeed.withValues(alpha: 0.3),
                        ),
                        child: Text(
                          widget.isEditing ? '수정 완료' : '저장하기',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  /// 백그라운드에서 단일 상품 Firebase 업로드를 스케줄링 (다중 등록과 동일한 패턴)
  void _scheduleBackgroundFirebaseUpload(Product product) {
    // 백그라운드에서 실행 (UI 블로킹 방지)
    Future.microtask(() async {
      try {
        LoggerUtils.logInfo('백그라운드 Firebase 업로드 시작: ${product.name}', tag: 'RegisterProductScreen');

        // 로컬 DB에서 저장된 상품 재조회 (ID 포함된 상태로)
        final repository = ref.read(productRepositoryProvider);
        final currentWorkspace = ref.read(currentWorkspaceProvider);
        
        if (currentWorkspace != null) {
          // 상품명으로 최근 저장된 상품 찾기
          final recentProducts = await repository.getProductsByEventId(currentWorkspace.id);
          final savedProduct = recentProducts.where((saved) => saved.name == product.name).lastOrNull;
          
          if (savedProduct != null) {
            // DataSyncService를 사용해서 Firebase에 업로드
            final dataSyncService = ref.read(dataSyncServiceProvider);
            await dataSyncService.uploadSingleProduct(savedProduct);
            LoggerUtils.logInfo('백그라운드 Firebase 업로드 성공: ${savedProduct.name} (ID: ${savedProduct.id})', tag: 'RegisterProductScreen');
          } else {
            LoggerUtils.logWarning('백그라운드 업로드용 상품을 찾을 수 없음: ${product.name}', tag: 'RegisterProductScreen');
          }
        }
      } catch (e) {
        LoggerUtils.logError('백그라운드 Firebase 업로드 실패: ${product.name}', tag: 'RegisterProductScreen', error: e);
      }
    });
  }
}

