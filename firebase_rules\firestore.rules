rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 사용자 문서: 본인만 읽기/쓰기 가능
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // 전화번호 업데이트 시 추가 검증
      allow update: if request.auth != null
        && request.auth.uid == userId
        && (
          // 전화번호가 변경되지 않거나
          !('phone' in request.resource.data) ||
          resource.data.phone == request.resource.data.phone ||
          // 새로운 전화번호인 경우 (중복 체크는 Functions에서 처리)
          request.resource.data.phone != null
        );
    }

    // SMS 인증 문서: 본인만 접근 가능
    match /sms_verifications/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // 관리자 전용 문서들
    match /admin/{document} {
      allow read, write: if request.auth != null &&
        request.auth.token.email in ['<EMAIL>'];
    }

    // 기타 모든 문서: 인증된 사용자만 접근 (기존 동작 유지)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}