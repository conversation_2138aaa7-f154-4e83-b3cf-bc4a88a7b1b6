@echo off
echo ========================================
echo    구글 플레이 콘솔용 AAB 빌드 스크립트
echo ========================================
echo.

REM 1. 환경 정리
echo [1/5] 빌드 캐시 정리 중...
flutter clean
if exist "android\.gradle" rd /s /q "android\.gradle"
if exist "build" rd /s /q "build"

echo.
echo [2/5] 키스토어 정보 입력
echo 키스토어 비밀번호: parabara7965
echo 키 비밀번호: parabara7965
echo 키 별칭: parabara-release

REM 2. key.properties 임시 생성 (Git 제외됨)
echo [3/5] 키스토어 설정 중...
(
echo storePassword=parabara7965
echo keyPassword=parabara7965
echo keyAlias=parabara-release
echo storeFile=upload-keystore.jks
) > "android\key.properties"

REM 3. 종속성 설치
echo [4/5] 종속성 설치 중...
flutter pub get

REM 4. 프로덕션 빌드
echo [5/5] 프로덕션 AAB 빌드 중...
flutter build appbundle --release --no-tree-shake-icons

REM 5. 보안 정리
echo.
echo [보안] key.properties 파일 삭제 중...
del "android\key.properties"
(
echo # 이 파일은 보안상 삭제되었습니다
echo # 환경 변수를 사용하거나 수동으로 입력하세요
) > "android\key.properties"

echo.
echo ✅ 빌드 완료!
if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo 📁 AAB 파일 위치: build\app\outputs\bundle\release\app-release.aab
    echo 🎯 이 파일을 구글 플레이 콘솔에 업로드하세요!
) else (
    echo ❌ AAB 파일 생성 실패
)
echo.
pause
