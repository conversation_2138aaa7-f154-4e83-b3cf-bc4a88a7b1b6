<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>관리자 대시보드 - 바라부스매니저</title>
    <link rel="icon" type="image/png" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #F8F9FA;
            color: #495057;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header Section */
        .header-section {
            background: white;
            border-bottom: 1px solid #E9ECEF;
            padding: 16px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #495057;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #495057;
            color: white;
        }

        .btn-primary:hover {
            background: #343A40;
        }

        .btn-secondary {
            background: #F8F9FA;
            color: #495057;
            border: 1px solid #DEE2E6;
        }

        .btn-secondary:hover {
            background: #E9ECEF;
        }

        /* Main Content */
        .main-content {
            padding: 24px 0;
        }

        /* Stats Cards - 원본과 동일하게 */
        .stats-grid {
            display: flex;
            gap: 16px;
            margin-bottom: 32px;
        }

        .stats-card {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
            border: 1px solid #E9ECEF;
        }

        .stats-card-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stats-icon {
            font-size: 32px;
            color: #6C757D;
            margin-right: auto;
        }

        .stats-value {
            font-size: 32px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .stats-title {
            font-size: 14px;
            color: #6C757D;
            font-weight: 500;
        }

        /* System Monitor */
        .system-monitor {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #E9ECEF;
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 20px;
        }

        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .monitor-item {
            text-align: center;
            padding: 16px;
            background: #F8F9FA;
            border-radius: 8px;
        }

        .monitor-label {
            font-size: 12px;
            color: #6C757D;
            margin-bottom: 4px;
        }

        .monitor-value {
            font-size: 18px;
            font-weight: 600;
            color: #495057;
        }

        /* Users Table */
        .users-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid #E9ECEF;
        }

        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 16px;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #DEE2E6;
            border-radius: 6px;
            font-size: 14px;
            min-width: 200px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #DEE2E6;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #E9ECEF;
        }

        .users-table th {
            background: #F8F9FA;
            font-weight: 600;
            color: #495057;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid #E9ECEF;
        }

        .pagination-info {
            font-size: 14px;
            color: #6C757D;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #495057;
        }

        .page-size-selector select {
            padding: 4px 8px;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            font-size: 14px;
        }

        .pagination-buttons {
            display: flex;
            gap: 4px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #CED4DA;
            background: white;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #E9ECEF;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #007BFF;
            color: white;
            border-color: #007BFF;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #D4EDDA;
            color: #155724;
        }

        .status-inactive {
            background: #F8D7DA;
            color: #721C24;
        }

        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn-view {
            background: #E3F2FD;
            color: #1976D2;
        }

        .action-btn-view:hover {
            background: #BBDEFB;
        }

        .action-btn-toggle {
            background: #FFF3E0;
            color: #F57C00;
        }

        .action-btn-toggle:hover {
            background: #FFE0B2;
        }

        /* 사용자 상세 모달 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 24px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 700;
            color: #495057;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #6C757D;
        }

        .close:hover {
            color: #495057;
        }

        .info-section {
            margin-bottom: 24px;
        }

        .info-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 16px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #F8F9FA;
        }

        .info-label {
            font-weight: 500;
            color: #6C757D;
        }

        .info-value {
            color: #495057;
        }

        /* Footer */
        .footer {
            background: white;
            border-top: 1px solid #E9ECEF;
            padding: 40px 24px;
            text-align: center;
            margin-top: 48px;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
        }

        .footer-logo-icon {
            padding: 8px;
            background: linear-gradient(135deg, #E09A74, #D08052);
            border-radius: 8px;
            margin-right: 12px;
            font-size: 20px;
        }

        .footer-logo-text {
            font-size: 18px;
            font-weight: 700;
            color: #495057;
        }

        .footer-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .footer-info-item {
            font-size: 14px;
            color: #6C757D;
        }

        .copyright {
            font-size: 12px;
            color: #ADB5BD;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 16px;
            }
            
            .header-actions {
                width: 100%;
                justify-content: center;
            }
            
            .table-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .footer-info {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <section class="header-section">
        <div class="container">
            <div class="header-content">
                <h1 class="header-title">바라부스매니저 관리자</h1>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="refreshData()">
                        🔄 새로고침
                    </button>
                    <button class="btn btn-secondary" onclick="viewLogs()">
                        📄 시스템 로그
                    </button>
                    <button class="btn btn-primary" onclick="logout()">
                        🚪 로그아웃
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="main-content">
        <div class="container">


            <!-- 시스템 모니터링 -->
            <div class="system-monitor">
                <h2 class="section-title">시스템 모니터링</h2>
                <div class="monitor-grid">
                    <div class="monitor-item">
                        <div class="monitor-label">총 함수 호출</div>
                        <div class="monitor-value" id="functionCalls">-</div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-label">평균 응답시간</div>
                        <div class="monitor-value" id="responseTime">-</div>
                    </div>
                    <div class="monitor-item">
                        <div class="monitor-label">오류율</div>
                        <div class="monitor-value" id="errorRate">-</div>
                    </div>
                </div>
            </div>

            <!-- 사용자 관리 -->
            <div class="users-section">
                <h2 class="section-title">사용자 관리</h2>
                <div class="table-controls">
                    <input type="text" class="search-input" placeholder="사용자 검색..." id="searchInput">
                    <select class="filter-select" id="statusFilter">
                        <option value="all">전체 상태</option>
                        <option value="active">활성</option>
                        <option value="inactive">비활성</option>
                    </select>
                </div>
                <div class="table-wrapper">
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th>이메일</th>
                                <th>닉네임</th>
                                <th>구독 상태</th>
                                <th>서버 사용량</th>
                                <th>가입일</th>
                                <th>최근 접속</th>
                                <th>작업</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 40px;">
                                    데이터를 불러오는 중...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 페이지네이션 -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span id="usersPaginationInfo">총 0개 중 0-0개 표시</span>
                    </div>
                    <div class="pagination-controls">
                        <div class="page-size-selector">
                            <span>페이지당</span>
                            <select id="usersPageSize" onchange="changeUsersPageSize(this.value)">
                                <option value="20">20개</option>
                                <option value="50">50개</option>
                                <option value="100">100개</option>
                            </select>
                        </div>
                        <div class="pagination-buttons" id="usersPaginationButtons">
                            <!-- 페이지 버튼들이 여기에 동적으로 생성됩니다 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- 사용자 상세보기 모달 -->
    <div id="userDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">사용자 상세 정보</h2>
                <span class="close" onclick="closeUserDetailModal()">&times;</span>
            </div>

            <div class="info-section">
                <div class="info-section-title">기본 정보</div>
                <div class="info-row">
                    <span class="info-label">UID</span>
                    <span class="info-value" id="modalUid">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">이메일</span>
                    <span class="info-value" id="modalEmail">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">닉네임</span>
                    <span class="info-value" id="modalNickname">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">전화번호</span>
                    <span class="info-value" id="modalPhone">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">가입일</span>
                    <span class="info-value" id="modalCreatedAt">-</span>
                </div>
            </div>

            <div class="info-section">
                <div class="info-section-title">구독 정보</div>
                <div class="info-row">
                    <span class="info-label">구독 상태</span>
                    <span class="info-value" id="modalSubscriptionStatus">-</span>
                </div>
            </div>

            <div class="info-section">
                <div class="info-section-title">사용량 정보</div>
                <div class="info-row">
                    <span class="info-label">서버 사용량</span>
                    <span class="info-value" id="modalServerUsage">-</span>
                </div>
            </div>

            <div style="text-align: right; margin-top: 24px;">
                <button class="btn btn-secondary" onclick="closeUserDetailModal()">닫기</button>
            </div>
        </div>
    </div>

    <script>
        // 전역 변수
        let currentPage = 1;
        let totalPages = 1;
        let pageSize = 20;
        let totalUsers = 0;
        let isLoading = false;
        let authToken = null;

        // API 엔드포인트
        const API_ENDPOINTS = {
            adminDashboard: 'https://admindashboard-kahfshl2oa-uc.a.run.app',
            adminUsers: 'https://adminusers-kahfshl2oa-uc.a.run.app',
            adminToggleSubscription: 'https://us-central1-parabara-1a504.cloudfunctions.net/adminToggleSubscription',
            getAdminSystemInfo: 'https://us-central1-parabara-1a504.cloudfunctions.net/getAdminSystemInfo'
        };

        // 인증 헤더 생성
        function getAuthHeaders() {
            return {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            };
        }

        // 토큰 확인 및 로그인 상태 검증
        function checkAuth() {
            authToken = localStorage.getItem('admin_token');
            if (!authToken) {
                window.location.href = '/admin-login.html';
                return false;
            }
            return true;
        }

        // 로딩 상태 설정
        function setLoading(loading) {
            isLoading = loading;
            const refreshBtn = document.querySelector('[onclick="refreshData()"]');
            if (refreshBtn) {
                refreshBtn.disabled = loading;
                refreshBtn.textContent = loading ? '🔄 로딩중...' : '🔄 새로고침';
            }
        }

        // 대시보드 통계 로드 (제거됨)
        async function loadDashboardStats() {
            // 통계 카드가 제거되어 더 이상 필요하지 않음
            return Promise.resolve();
        }

        // 시스템 정보 로드
        async function loadSystemInfo() {
            try {
                const response = await fetch(API_ENDPOINTS.getAdminSystemInfo);
                const data = await response.json();

                if (data.success && data.data) {
                    const systemInfo = data.data;

                    // systemStats가 있는 경우 해당 정보 사용
                    if (systemInfo.systemStats) {
                        const stats = systemInfo.systemStats;
                        document.getElementById('functionCalls').textContent = stats.totalFunctionCalls || '-';
                        document.getElementById('responseTime').textContent = stats.averageResponseTime || '-';
                        document.getElementById('errorRate').textContent = stats.errorRate || '-';
                    } else {
                        // 기본값 설정
                        document.getElementById('functionCalls').textContent = '-';
                        document.getElementById('responseTime').textContent = '-';
                        document.getElementById('errorRate').textContent = '-';
                    }
                } else {
                    // 데이터가 없는 경우 기본값 설정
                    document.getElementById('functionCalls').textContent = '-';
                    document.getElementById('responseTime').textContent = '-';
                    document.getElementById('errorRate').textContent = '-';
                }
            } catch (error) {
                console.error('시스템 정보 로드 실패:', error);
                // 오류 발생 시 기본값 설정
                document.getElementById('functionCalls').textContent = '오류';
                document.getElementById('responseTime').textContent = '오류';
                document.getElementById('errorRate').textContent = '오류';
            }
        }

        // 사용자 목록 로드
        async function loadUsers(page = 1, limit = 20) {
            try {
                const response = await fetch(`${API_ENDPOINTS.adminUsers}?page=${page}&limit=${limit}`, {
                    headers: getAuthHeaders()
                });

                if (response.status === 401) {
                    localStorage.removeItem('admin_token');
                    window.location.href = '/admin-login.html';
                    return;
                }

                const data = await response.json();
                if (data.success && data.data) {
                    const { users, pagination } = data.data;
                    displayUsers(users);
                    updateUsersPagination(pagination);
                }
            } catch (error) {
                console.error('사용자 목록 로드 실패:', error);
                showError('사용자 목록을 불러오는데 실패했습니다.');
            }
        }

        // 사용자 목록 표시
        function displayUsers(users) {
            const tbody = document.getElementById('usersTableBody');
            if (!users || users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 40px;">사용자가 없습니다.</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => `
                <tr>
                    <td>${user.email || 'N/A'}</td>
                    <td>${user.displayName || user.nickname || 'N/A'}</td>
                    <td>
                        <span class="status-badge ${user.subscriptionStatus === 'premium' ? 'status-active' : 'status-inactive'}">
                            ${user.subscriptionStatus === 'premium' ? '프리미엄' : '무료'}
                        </span>
                    </td>
                    <td>${user.serverUsage || '데이터 없음'}</td>
                    <td>${user.createdAt ? formatUserDate(user.createdAt) : 'N/A'}</td>
                    <td>${user.lastLoginAt ? formatUserDate(user.lastLoginAt) : 'N/A'}</td>
                    <td>
                        <button class="action-btn action-btn-view" onclick="showUserDetails('${user.uid}', '${user.email}', '${user.displayName || user.nickname || 'N/A'}', '${user.subscriptionStatus || 'free'}', '${user.serverUsage || '데이터 없음'}', '${user.createdAt || ''}', '${user.phone || 'N/A'}')">
                            상세보기
                        </button>
                        <button class="action-btn action-btn-toggle" onclick="toggleUserSubscription('${user.uid}', '${user.subscriptionStatus || 'free'}')">
                            구독변경
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 사용자 날짜 포맷
        function formatUserDate(dateValue) {
            if (!dateValue) return 'N/A';

            try {
                let date;
                if (typeof dateValue === 'string') {
                    date = new Date(dateValue);
                } else if (typeof dateValue === 'object' && dateValue._seconds) {
                    date = new Date(dateValue._seconds * 1000);
                } else if (typeof dateValue === 'number') {
                    date = new Date(dateValue > 1000000000000 ? dateValue : dateValue * 1000);
                } else {
                    date = new Date(dateValue);
                }

                if (isNaN(date.getTime())) {
                    return 'N/A';
                }

                return date.toLocaleDateString('ko-KR');
            } catch (error) {
                return 'N/A';
            }
        }

        // 페이지네이션 업데이트
        function updatePagination(pagination) {
            if (pagination) {
                currentPage = pagination.currentPage || 1;
                totalPages = pagination.totalPages || 1;
            }
        }

        // 에러 메시지 표시
        function showError(message) {
            // 간단한 알림으로 표시 (실제로는 더 나은 UI 구현 가능)
            alert(message);
        }

        // 사용자 페이지네이션 업데이트
        function updateUsersPagination(pagination) {
            if (!pagination) return;

            currentPage = pagination.currentPage;
            totalPages = pagination.totalPages;
            totalUsers = pagination.totalUsers;

            // 페이지네이션 정보 업데이트
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, totalUsers);
            document.getElementById('usersPaginationInfo').textContent =
                `총 ${totalUsers}개 중 ${startIndex}-${endIndex}개 표시`;

            // 페이지네이션 버튼 생성
            generateUsersPaginationButtons();
        }

        // 사용자 페이지네이션 버튼 생성
        function generateUsersPaginationButtons() {
            const container = document.getElementById('usersPaginationButtons');
            let buttons = '';

            // 첫 페이지 버튼
            buttons += `<button class="pagination-btn" onclick="changeUsersPage(1)" ${currentPage === 1 ? 'disabled' : ''}>첫 페이지</button>`;

            // 이전 버튼
            buttons += `<button class="pagination-btn" onclick="changeUsersPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>이전</button>`;

            // 페이지 번호 버튼들
            const maxButtons = 5;
            let start = Math.max(1, currentPage - Math.floor(maxButtons / 2));
            let end = Math.min(totalPages, start + maxButtons - 1);

            if (end - start + 1 < maxButtons) {
                start = Math.max(1, end - maxButtons + 1);
            }

            for (let i = start; i <= end; i++) {
                buttons += `<button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changeUsersPage(${i})">${i}</button>`;
            }

            // 다음 버튼
            buttons += `<button class="pagination-btn" onclick="changeUsersPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>다음</button>`;

            // 마지막 페이지 버튼
            buttons += `<button class="pagination-btn" onclick="changeUsersPage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>마지막 페이지</button>`;

            container.innerHTML = buttons;
        }

        // 사용자 페이지 변경
        async function changeUsersPage(page) {
            if (page < 1 || page > totalPages || page === currentPage || isLoading) return;

            currentPage = page;
            await loadUsers(currentPage, pageSize);
        }

        // 사용자 페이지 크기 변경
        async function changeUsersPageSize(size) {
            pageSize = parseInt(size);
            currentPage = 1; // 첫 페이지로 리셋
            await loadUsers(currentPage, pageSize);
        }

        // 모든 데이터 새로고침
        async function refreshData() {
            if (isLoading) return;

            setLoading(true);
            try {
                await Promise.all([
                    loadDashboardStats(),
                    loadSystemInfo(),
                    loadUsers(currentPage, pageSize)
                ]);
            } finally {
                setLoading(false);
            }
        }

        // 시스템 로그 페이지로 이동
        function viewLogs() {
            window.location.href = '/admin-logs.html';
        }

        // 로그아웃
        function logout() {
            if (confirm('로그아웃하시겠습니까?')) {
                localStorage.removeItem('admin_token');
                window.location.href = '/admin-login.html';
            }
        }

        // 검색 기능
        function handleSearch() {
            const searchQuery = document.getElementById('searchInput').value.trim();
            // 실제로는 검색 API 호출
            console.log('검색:', searchQuery);
        }

        // 필터 변경
        function handleFilterChange() {
            const filter = document.getElementById('statusFilter').value;
            // 실제로는 필터링된 사용자 목록 로드
            console.log('필터:', filter);
        }

        // 페이지 로드 시 초기화
        document.addEventListener('DOMContentLoaded', async function() {
            if (!checkAuth()) return;

            // 검색 및 필터 이벤트 리스너 등록
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');

            if (searchInput) {
                searchInput.addEventListener('input', debounce(handleSearch, 500));
            }

            if (statusFilter) {
                statusFilter.addEventListener('change', handleFilterChange);
            }

            // 초기 데이터 로드
            await refreshData();
        });

        // 사용자 상세보기 모달 표시
        function showUserDetails(uid, email, nickname, subscriptionStatus, serverUsage, createdAt, phone) {
            document.getElementById('modalUid').textContent = uid;
            document.getElementById('modalEmail').textContent = email;
            document.getElementById('modalNickname').textContent = nickname;
            document.getElementById('modalPhone').textContent = phone;
            document.getElementById('modalCreatedAt').textContent = createdAt ? formatUserDate(createdAt) : 'N/A';
            document.getElementById('modalSubscriptionStatus').textContent = subscriptionStatus === 'premium' ? '프리미엄' : '무료';
            document.getElementById('modalServerUsage').textContent = serverUsage;

            document.getElementById('userDetailModal').style.display = 'block';
        }

        // 사용자 상세보기 모달 닫기
        function closeUserDetailModal() {
            document.getElementById('userDetailModal').style.display = 'none';
        }

        // 구독 상태 변경
        async function toggleUserSubscription(uid, currentStatus) {
            if (!confirm('이 사용자의 구독 상태를 변경하시겠습니까?')) {
                return;
            }

            try {
                const action = currentStatus === 'premium' ? 'deactivate' : 'activate';

                const response = await fetch(API_ENDPOINTS.adminToggleSubscription, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        uid: uid,
                        action: action
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('구독 상태가 변경되었습니다.');
                    await refreshData(); // 데이터 새로고침
                } else {
                    alert('구독 상태 변경에 실패했습니다: ' + (data.message || '알 수 없는 오류'));
                }
            } catch (error) {
                console.error('구독 상태 변경 오류:', error);
                alert('구독 상태 변경 중 오류가 발생했습니다.');
            }
        }

        // 모달 외부 클릭 시 닫기
        window.onclick = function(event) {
            const modal = document.getElementById('userDetailModal');
            if (event.target === modal) {
                closeUserDetailModal();
            }
        }

        // 디바운스 함수
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
