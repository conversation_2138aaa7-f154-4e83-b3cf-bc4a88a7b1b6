import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/admin_service.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/logger_utils.dart';
import '../../widgets/onboarding_components.dart';
import '../../providers/nickname_provider.dart';

/// 소셜 로그인 전 약관 동의 화면
class SocialTermsAgreementScreen extends ConsumerStatefulWidget {
  final String socialType; // 'google' 또는 'apple'
  final String? userEmail; // 사용자 이메일 (미리 받은 경우)
  final VoidCallback onLoginSuccess;
  final VoidCallback onCancel;

  const SocialTermsAgreementScreen({
    super.key,
    required this.socialType,
    this.userEmail,
    required this.onLoginSuccess,
    required this.onCancel,
  });

  @override
  ConsumerState<SocialTermsAgreementScreen> createState() => _SocialTermsAgreementScreenState();
}

class _SocialTermsAgreementScreenState extends ConsumerState<SocialTermsAgreementScreen> {
  static const String _tag = 'SocialTermsAgreementScreen';
  
  bool agreeAll = false;
  bool agreeTermsOfService = false;
  bool agreePrivacyPolicy = false;
  bool agreeMarketing = false;
  bool isLoading = false;
  String? error;

  static const String termsOfService = '''바라 부스 매니저 서비스 이용약관

제1조 (목적)
이 약관은 바라 부스 매니저(이하 "서비스")를 제공하는 권태영(이하 "회사")과 서비스를 이용하는 회원 간의 권리, 의무 및 책임사항을 규정함을 목적으로 합니다.

제2조 (정의)
1. "서비스"란 회사가 제공하는 동인 행사 부스 관리 모바일 애플리케이션 및 관련 서비스를 의미합니다.
2. "회원"이란 이 약관에 동의하고 회사와 서비스 이용계약을 체결한 개인을 의미합니다.
3. "부스 데이터"란 회원이 서비스를 통해 입력, 저장하는 상품정보, 판매기록, 재고정보 등을 의미합니다.

제3조 (약관의 효력 및 변경)
1. 이 약관은 서비스 화면에 게시하거나 기타의 방법으로 회원에게 공지함으로써 효력이 발생합니다.
2. 회사는 필요한 경우 이 약관을 변경할 수 있으며, 변경된 약관은 제1항과 같은 방법으로 공지 또는 통지함으로써 효력이 발생합니다.

제4조 (서비스의 제공)
1. 회사는 다음과 같은 서비스를 제공합니다:
   - 상품 등록 및 재고 관리
   - 판매 기록 및 매출 통계
   - 선입금 관리
   - 행사별 데이터 관리
   - 클라우드 데이터 동기화
2. 서비스는 연중무휴, 1일 24시간 제공함을 원칙으로 합니다.
3. 회사는 시스템 점검, 보수 또는 기타 필요에 의해 서비스 제공을 일시 중단할 수 있습니다.

문의: <EMAIL>
전화: 070-8080-4308''';

  static const String privacyPolicy = '''바라 부스 매니저 개인정보처리방침

제1조 (개인정보의 처리목적)
회사는 다음의 목적을 위하여 개인정보를 처리합니다:
1. 회원가입 및 관리
2. 서비스 제공 및 계약 이행
3. 고객 상담 및 불만 처리
4. 마케팅 및 광고 활용 (동의 시)

제2조 (처리하는 개인정보의 항목)
1. 필수항목: 이메일 주소, 비밀번호(암호화 저장)
2. 선택항목 (구독 서비스 이용 시 필수):
   - 전화번호: 본인 인증, 구독 서비스 이용 자격 확인, 고객 지원
   - ※ 전화번호는 구독 서비스 이용을 위해 필수적으로 수집됩니다.
   - ※ 동일한 전화번호로 여러 계정에서 구독 서비스를 이용할 수 없습니다.
3. 자동수집: 서비스 이용기록, 접속로그, 쿠키, 접속IP정보

제3조 (개인정보의 처리 및 보유기간)
1. 회원탈퇴 시까지 보유
2. 전화번호: 구독 서비스 해지 또는 회원탈퇴 시까지 보유
3. 관계법령에 따른 보존의무가 있는 경우 해당 기간까지 보유

제4조 (개인정보의 제3자 제공)
회사는 원칙적으로 개인정보를 외부에 제공하지 않습니다. 다만, 다음의 경우에는 예외로 합니다:
1. 정보주체가 사전에 동의한 경우
2. 법령의 규정에 의거하거나, 수사 목적으로 법령에 정해진 절차와 방법에 따라 수사기관의 요구가 있는 경우

제5조 (개인정보처리의 위탁)
회사는 서비스 제공을 위해 다음과 같이 개인정보 처리를 위탁합니다:
- Google Firebase: 데이터 저장 및 인증 서비스
- 네이버 클라우드 플랫폼: SMS 발송 서비스 (전화번호 인증 시)

제6조 (정보주체의 권리)
정보주체는 언제든지 개인정보 처리현황에 대한 열람, 정정·삭제, 처리정지를 요구할 수 있습니다.

문의: <EMAIL>
전화: 070-8080-4308''';

  void _updateAgreeAll() {
    setState(() {
      agreeAll = agreeTermsOfService && agreePrivacyPolicy;
    });
  }

  void _handleAgreeAll(bool? value) {
    setState(() {
      agreeAll = value ?? false;
      agreeTermsOfService = agreeAll;
      agreePrivacyPolicy = agreeAll;
      // 마케팅은 전체 동의에 포함하지 않음
    });
  }

  void _showTermsDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: Text(content),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  /// 특정 소셜 타입으로 로그인 진행 (소셜 간편 로그인에서 사용)
  Future<void> _proceedWithSpecificSocialLogin(String socialType) async {
    if (!agreeTermsOfService || !agreePrivacyPolicy) {
      setState(() {
        error = '필수 약관에 동의해주세요.';
      });
      return;
    }

    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      UserCredential? userCredential;
      String? email;

      if (socialType == 'google') {
        final result = await _performGoogleSignIn();
        userCredential = result['userCredential'];
        email = result['email'];
      } else if (socialType == 'apple') {
        userCredential = await _performAppleSignIn();
        email = userCredential?.user?.email;
      }

      if (userCredential?.user == null) {
        throw Exception('로그인에 실패했습니다.');
      }

      // 소셜 로그인 성공 처리
      await _handleSocialLoginSuccess(userCredential!.user!, email);

      // 접속 로그 기록
      try {
        await AdminService.logAccess(userCredential.user!.uid, null);
      } catch (e) {
        LoggerUtils.logWarning('접속 로그 기록 실패', tag: _tag, error: e);
      }

      LoggerUtils.logInfo('$socialType 로그인 성공: $email', tag: _tag);
      widget.onLoginSuccess();

    } catch (e) {
      LoggerUtils.logError('$socialType 로그인 실패', tag: _tag, error: e);
      setState(() {
        error = e.toString().replaceAll('Exception: ', '');
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  /// 약관 동의 후 소셜 로그인 진행 (기존 방식)
  Future<void> _proceedWithSocialLogin() async {
    if (!agreeTermsOfService || !agreePrivacyPolicy) {
      setState(() {
        error = '필수 약관에 동의해주세요.';
      });
      return;
    }

    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      UserCredential? userCredential;
      String? email;

      if (widget.socialType == 'google') {
        final result = await _performGoogleSignIn();
        userCredential = result['userCredential'];
        email = result['email'];
      } else if (widget.socialType == 'apple') {
        userCredential = await _performAppleSignIn();
        email = userCredential?.user?.email;
      }

      if (userCredential != null && userCredential.user != null) {
        // Firestore에 사용자 정보 및 약관 동의 정보 저장
        await FirebaseFirestore.instance
            .collection('users')
            .doc(userCredential.user!.uid)
            .set({
          'email': email ?? widget.userEmail ?? '',
          'createdAt': FieldValue.serverTimestamp(),
          'agreement': {
            'agreed': true,
            'agreedAt': FieldValue.serverTimestamp(),
            'termsOfService': agreeTermsOfService,
            'privacyPolicy': agreePrivacyPolicy,
            'marketing': agreeMarketing,
          },
        }, SetOptions(merge: true));

        // 소셜 로그인 성공 후 공통 처리 (닉네임 로드 등)
        await _handleSocialLoginSuccess(userCredential.user!, email ?? widget.userEmail);

        LoggerUtils.logInfo('소셜 로그인 및 약관 동의 완료: ${widget.socialType}', tag: _tag);
        widget.onLoginSuccess();
      }
    } catch (e) {
      LoggerUtils.logError('소셜 로그인 실패', tag: _tag, error: e);
      setState(() {
        error = '로그인에 실패했습니다: ${e.toString()}';
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  /// Google 로그인 수행
  Future<Map<String, dynamic>> _performGoogleSignIn() async {
    final GoogleSignIn signIn = GoogleSignIn.instance;
    await signIn.initialize(
      serverClientId: '************-0kgl0snkmk62ecrfkmoka6jq3dscj4he.apps.googleusercontent.com',
    );

    final GoogleSignInAccount? googleUser = await signIn.authenticate();
    if (googleUser == null) {
      throw Exception('Google 로그인이 취소되었습니다.');
    }

    final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
    final credential = GoogleAuthProvider.credential(
      idToken: googleAuth.idToken,
    );

    final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

    return {
      'userCredential': userCredential,
      'email': googleUser.email,
    };
  }

  /// Apple 로그인 수행
  Future<UserCredential?> _performAppleSignIn() async {
    final appleProvider = AppleAuthProvider();
    appleProvider.addScope('email');
    appleProvider.addScope('name');
    appleProvider.setCustomParameters({'locale': 'ko'});

    return await FirebaseAuth.instance.signInWithProvider(appleProvider);
  }

  /// 소셜 로그인 성공 후 공통 처리
  Future<void> _handleSocialLoginSuccess(User user, String? email) async {
    try {
      // Firestore 사용자 문서 생성/업데이트
      await _createUserDocumentIfNeededWithEmail(user, email);

      // 소셜 로그인 성공 시 nicknameProvider 동기화
      await ref.read(nicknameProvider.notifier).loadNickname();

      LoggerUtils.logInfo('소셜 로그인 초기화 완료: $email', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('소셜 로그인 후처리 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// Firestore 사용자 문서 생성/업데이트 (이메일 포함)
  Future<void> _createUserDocumentIfNeededWithEmail(User user, String? email) async {
    try {
      final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);
      final userDoc = await userRef.get();

      if (!userDoc.exists) {
        // 신규 사용자 문서 생성
        await userRef.set({
          'email': email ?? user.email ?? '',
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        LoggerUtils.logInfo('신규 사용자 문서 생성 완료: ${user.uid}', tag: _tag);
      } else {
        // 기존 사용자 문서 업데이트 (이메일 정보 보완)
        await userRef.update({
          'email': email ?? user.email ?? userDoc.data()?['email'] ?? '',
          'updatedAt': FieldValue.serverTimestamp(),
        });
        LoggerUtils.logInfo('기존 사용자 문서 업데이트 완료: ${user.uid}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('사용자 문서 처리 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.onboardingTextPrimary,
          ),
          onPressed: () {
            widget.onCancel();
          },
        ),
      ),
      body: OnboardingComponents.buildBackground(
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: OnboardingComponents.buildCard(
                context: context,
                child: _buildTermsForm(context),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTermsForm(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 제목
        Text(
          '소셜 계정으로 로그인',
          style: TextStyle(
            fontSize: ResponsiveHelper.getTitleFontSize(context),
            fontWeight: FontWeight.bold,
            color: AppColors.onboardingTextPrimary,
          ),
        ),
        
        const SizedBox(height: 12),
        
        Text(
          '서비스 이용을 위해 약관에 동의해주세요.',
          style: TextStyle(
            fontSize: ResponsiveHelper.getBodyFontSize(context),
            color: AppColors.onboardingTextSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 24),
        
        // 전체 동의 체크박스
        _buildAgreeAllCheckbox(context),
        
        const Divider(height: 32),
        
        // 개별 약관 체크박스들
        _buildIndividualAgreements(context),
        
        const SizedBox(height: 24),
        
        // 에러 메시지
        if (error != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Text(
              error!,
              style: TextStyle(
                color: AppColors.error,
                fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        
        // 버튼들
        _buildButtons(context),
      ],
    );
  }

  Widget _buildAgreeAllCheckbox(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.surface.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.onboardingPrimary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: agreeAll,
            onChanged: _handleAgreeAll,
            activeColor: AppColors.onboardingPrimary,
          ),
          Expanded(
            child: Text(
              '전체 동의',
              style: TextStyle(
                fontSize: ResponsiveHelper.getBodyFontSize(context),
                fontWeight: FontWeight.w600,
                color: AppColors.onboardingTextPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndividualAgreements(BuildContext context) {
    return Column(
      children: [
        _buildAgreementItem(
          context: context,
          title: '[필수] 서비스 이용약관',
          isRequired: true,
          isChecked: agreeTermsOfService,
          onChanged: (value) {
            setState(() {
              agreeTermsOfService = value ?? false;
              _updateAgreeAll();
            });
          },
          onViewDetails: () => _showTermsDialog('서비스 이용약관', termsOfService),
        ),

        const SizedBox(height: 12),

        _buildAgreementItem(
          context: context,
          title: '[필수] 개인정보처리방침',
          isRequired: true,
          isChecked: agreePrivacyPolicy,
          onChanged: (value) {
            setState(() {
              agreePrivacyPolicy = value ?? false;
              _updateAgreeAll();
            });
          },
          onViewDetails: () => _showTermsDialog('개인정보처리방침', privacyPolicy),
        ),

        const SizedBox(height: 12),

        _buildAgreementItem(
          context: context,
          title: '[선택] 마케팅 정보 수신 동의',
          isRequired: false,
          isChecked: agreeMarketing,
          onChanged: (value) {
            setState(() {
              agreeMarketing = value ?? false;
            });
          },
          onViewDetails: () => _showTermsDialog(
            '마케팅 정보 수신 동의',
            '서비스 관련 이벤트, 혜택, 새로운 기능 등의 마케팅 정보를 이메일로 받아보실 수 있습니다.\n\n언제든지 설정에서 수신을 거부하실 수 있습니다.',
          ),
        ),
      ],
    );
  }

  Widget _buildAgreementItem({
    required BuildContext context,
    required String title,
    required bool isRequired,
    required bool isChecked,
    required ValueChanged<bool?> onChanged,
    required VoidCallback onViewDetails,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        children: [
          Checkbox(
            value: isChecked,
            onChanged: onChanged,
            activeColor: AppColors.onboardingPrimary,
          ),
          Expanded(
            child: GestureDetector(
              onTap: onViewDetails,
              child: Text(
                title,
                style: TextStyle(
                  fontSize: ResponsiveHelper.getBodyFontSize(context) - 1,
                  color: AppColors.onboardingTextPrimary,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),
          IconButton(
            onPressed: onViewDetails,
            icon: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.onboardingTextSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons(BuildContext context) {
    // 소셜 간편 로그인으로 들어온 경우 구글/애플 버튼 표시
    if (widget.socialType == 'social') {
      return Column(
        children: [
          // 구글 로그인 버튼
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton.icon(
              onPressed: (isLoading || !agreeTermsOfService || !agreePrivacyPolicy)
                  ? null
                  : () => _proceedWithSpecificSocialLogin('google'),
              icon: Image.asset(
                'assets/icons/Google_logo.png',
                width: 20,
                height: 20,
              ),
              label: const Text('Google로 로그인'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.black87,
                side: BorderSide(color: Colors.grey.shade300),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 1,
              ),
            ),
          ),

          const SizedBox(height: 12),

          // 애플 로그인 버튼
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton.icon(
              onPressed: (isLoading || !agreeTermsOfService || !agreePrivacyPolicy)
                  ? null
                  : () => _proceedWithSpecificSocialLogin('apple'),
              icon: Image.asset(
                'assets/icons/Apple_logo.png',
                width: 20,
                height: 20,
              ),
              label: const Text('Apple로 로그인'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.black87,
                side: BorderSide(color: Colors.grey.shade300),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 1,
              ),
            ),
          ),
        ],
      );
    }

    // 기존 방식 (특정 소셜 타입으로 들어온 경우)
    return Column(
      children: [
        // 동의하고 로그인 버튼
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: isLoading ? null : _proceedWithSocialLogin,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.onboardingPrimary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    '동의하고 ${widget.socialType == 'google' ? 'Google' : 'Apple'}로 로그인',
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getBodyFontSize(context),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),

        const SizedBox(height: 12),

        // 취소 버튼
        SizedBox(
          width: double.infinity,
          height: 48,
          child: TextButton(
            onPressed: isLoading ? null : widget.onCancel,
            style: TextButton.styleFrom(
              foregroundColor: AppColors.onboardingTextSecondary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: AppColors.onboardingTextSecondary.withOpacity(0.3),
                ),
              ),
            ),
            child: Text(
              '취소',
              style: TextStyle(
                fontSize: ResponsiveHelper.getBodyFontSize(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
