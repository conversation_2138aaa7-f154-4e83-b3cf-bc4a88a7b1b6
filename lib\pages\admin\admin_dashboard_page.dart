import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../services/admin_service.dart';
import '../../models/admin_models.dart';
import 'widgets/admin_stats_card.dart';
import 'widgets/admin_users_table.dart';
import 'widgets/admin_system_monitor.dart';
import 'admin_logs_page.dart';
import 'widgets/user_detail_dialog.dart';

/// 관리자 대시보드 페이지
class AdminDashboardPage extends StatefulWidget {
  const AdminDashboardPage({super.key});

  @override
  State<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends State<AdminDashboardPage> {
  AdminDashboardStats? _stats;
  List<AdminUser> _users = [];
  AdminPagination? _pagination;
  List<SystemLog> _logs = [];
  SystemStats? _systemStats;
  bool _isLoading = true;
  String _searchQuery = '';
  String _statusFilter = 'all';
  String _sortBy = 'createdAt';
  String _sortOrder = 'desc';
  int _currentPage = 1;
  final int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final futures = await Future.wait([
        AdminService.getDashboardStats(),
        AdminService.getUsers(page: _currentPage, limit: _pageSize),
        AdminService.getSystemLogs(),
        AdminService.getSystemStats(),
      ]);

      setState(() {
        _stats = futures[0] as AdminDashboardStats?;
        final usersResponse = futures[1] as AdminUsersResponse;
        _users = usersResponse.users;
        _pagination = usersResponse.pagination;
        _logs = futures[2] as List<SystemLog>;
        _systemStats = futures[3] as SystemStats?;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('데이터 로드 중 오류가 발생했습니다: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _logout() async {
    await AdminService.logout();
    if (mounted) {
      context.go('/');
    }
  }

  Future<void> _loadUsersPage(int page) async {
    setState(() {
      _currentPage = page;
      _isLoading = true;
    });

    try {
      final usersResponse = await AdminService.getUsers(page: page, limit: _pageSize);
      setState(() {
        _users = usersResponse.users;
        _pagination = usersResponse.pagination;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('사용자 목록 로드 중 오류가 발생했습니다: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showUserDetails(AdminUser user) {
    showDialog(
      context: context,
      builder: (context) => UserDetailDialog(user: user),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          '바라부스매니저 관리자',
          style: TextStyle(
            color: Color(0xFF495057),
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Color(0xFF495057)),
            onPressed: _loadData,
            tooltip: '새로고침',
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.article_outlined, color: Color(0xFF495057)),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AdminLogsPage(),
                ),
              );
            },
            tooltip: '시스템 로그',
          ),
          const SizedBox(width: 8),
          TextButton.icon(
            onPressed: _logout,
            icon: const Icon(Icons.logout, color: Color(0xFF495057)),
            label: const Text(
              '로그아웃',
              style: TextStyle(color: Color(0xFF495057)),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF495057),
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                  // 통계 카드들
                  if (_stats != null) ...[
                    Row(
                      children: [
                        Expanded(
                          child: AdminStatsCard(
                            title: '전체 사용자',
                            value: _stats!.totalUsers.toString(),
                            icon: Icons.people,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: AdminStatsCard(
                            title: '활성 구독자',
                            value: _stats!.activeSubscribers.toString(),
                            icon: Icons.star,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: AdminStatsCard(
                            title: '총 매출',
                            value: '${_stats!.totalRevenue.toStringAsFixed(0)}원',
                            icon: Icons.attach_money,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: AdminStatsCard(
                            title: '무료 사용자',
                            value: '${_stats!.freeUsers}명',
                            icon: Icons.people_outline,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                  ],

                  // 시스템 모니터링
                  AdminSystemMonitor(
                    logs: _logs,
                    stats: _systemStats,
                    dashboardStats: _stats,
                    onRefresh: _loadData,
                  ),
                  const SizedBox(height: 32),

                  // 사용자 관리
                  AdminUsersTable(
                    users: _users,
                    searchQuery: _searchQuery,
                    statusFilter: _statusFilter,
                    sortBy: _sortBy,
                    sortOrder: _sortOrder,
                    currentPage: _pagination?.currentPage ?? 1,
                    totalPages: _pagination?.totalPages ?? 1,
                    totalUsers: _pagination?.totalUsers ?? 0,
                    onSearchChanged: (query) {
                      setState(() {
                        _searchQuery = query;
                        _currentPage = 1;
                      });
                      _loadData();
                    },
                    onStatusFilterChanged: (filter) {
                      setState(() {
                        _statusFilter = filter;
                        _currentPage = 1;
                      });
                      _loadData();
                    },
                    onSortChanged: (sortBy, sortOrder) {
                      setState(() {
                        _sortBy = sortBy;
                        _sortOrder = sortOrder;
                      });
                      _loadData();
                    },
                    onViewDetails: _showUserDetails,
                    onPageChanged: _loadUsersPage,
                    onToggleSubscription: (user) async {
                      final success = await AdminService.toggleSubscription(
                        user.uid,
                        user.subscription?.status ?? 'free',
                      );
                      
                      if (success) {
                        await _loadData(); // 데이터 새로고침
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('구독 상태가 변경되었습니다.'),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      } else {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('구독 상태 변경에 실패했습니다.'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                  ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
