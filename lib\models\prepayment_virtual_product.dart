class PrepaymentVirtualProduct {
  final int id;
  final String name;
  final double price;
  final int quantity;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int eventId; // 행사 ID 추가

  const PrepaymentVirtualProduct({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    required this.createdAt,
    this.updatedAt,
    required this.eventId, // 행사 ID 필수
  });
  
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'quantity': quantity,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'eventId': eventId,
    };
  }

  factory PrepaymentVirtualProduct.fromMap(Map<String, dynamic> map) {
    return PrepaymentVirtualProduct(
      id: map['id'] as int,
      name: map['name'] as String,
      price: (map['price'] as num).toDouble(),
      quantity: map['quantity'] as int,
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt'] as String) : null,
      eventId: map['eventId'] as int? ?? 1, // 기본값 1
    );
  }

  PrepaymentVirtualProduct copyWith({
    int? id,
    String? name,
    double? price,
    int? quantity,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? eventId,
  }) {
    return PrepaymentVirtualProduct(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      eventId: eventId ?? this.eventId,
    );
  }

  @override
  String toString() {
    return 'PrepaymentVirtualProduct(id: $id, name: $name, price: $price, quantity: $quantity, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrepaymentVirtualProduct &&
        other.id == id &&
        other.name == name &&
        other.price == price &&
        other.quantity == quantity &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        price.hashCode ^
        quantity.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
