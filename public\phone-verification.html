<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>전화번호 인증 - 바라 부스 매니저</title>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #FAFAFA;
            color: #1F2937;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.02);
            border: 1px solid #F3F4F6;
        }

        .title {
            font-size: 24px;
            font-weight: 700;
            color: #1F2937;
            text-align: center;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #6B7280;
            text-align: center;
            margin-bottom: 32px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #E09A74;
            box-shadow: 0 0 0 3px rgba(224, 154, 116, 0.1);
        }

        .phone-input-group {
            display: flex;
            gap: 8px;
        }

        .phone-input {
            flex: 1;
        }

        .send-btn {
            padding: 12px 20px;
            background-color: #E09A74;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }

        .send-btn:hover:not(:disabled) {
            background-color: #D08052;
        }

        .send-btn:disabled {
            background-color: #9CA3AF;
            cursor: not-allowed;
        }

        .verify-btn {
            width: 100%;
            padding: 16px;
            background-color: #6366F1;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 16px;
        }

        .verify-btn:hover:not(:disabled) {
            background-color: #5856EB;
        }

        .verify-btn:disabled {
            background-color: #9CA3AF;
            cursor: not-allowed;
        }

        .recaptcha-container {
            margin: 24px 0;
            display: flex;
            justify-content: center;
        }

        .message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 14px;
        }

        .message.success {
            background-color: #F0FDF4;
            color: #166534;
            border: 1px solid #BBF7D0;
        }

        .message.error {
            background-color: #FEF2F2;
            color: #DC2626;
            border: 1px solid #FECACA;
        }

        .message.info {
            background-color: #F0F9FF;
            color: #1E40AF;
            border: 1px solid #BFDBFE;
        }

        .timer {
            font-size: 12px;
            color: #DC2626;
            margin-top: 4px;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #E09A74;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .container {
                margin: 0 10px;
                padding: 24px;
            }
            
            .phone-input-group {
                flex-direction: column;
            }
            
            .send-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">전화번호 인증</h1>
        <p class="subtitle">본인 확인을 위해 전화번호를 인증해주세요</p>

        <div id="messageContainer"></div>

        <form id="phoneVerificationForm">
            <div class="form-group">
                <label for="phoneNumber" class="form-label">전화번호</label>
                <div class="phone-input-group">
                    <input 
                        type="tel" 
                        id="phoneNumber" 
                        class="form-input phone-input" 
                        placeholder="010-1234-5678"
                        maxlength="13"
                        required
                    >
                    <button type="button" id="sendCodeBtn" class="send-btn">
                        인증번호 발송
                    </button>
                </div>
            </div>

            <!-- reCAPTCHA -->
            <div class="recaptcha-container">
                <div class="g-recaptcha" data-sitekey="6LdfCK4rAAAAAFjdN1h44yAUhSj4aLeklB3U1DO_"></div>
            </div>

            <div class="form-group hidden" id="verificationGroup">
                <label for="verificationCode" class="form-label">인증번호</label>
                <input 
                    type="text" 
                    id="verificationCode" 
                    class="form-input" 
                    placeholder="6자리 인증번호 입력"
                    maxlength="6"
                >
                <div id="timer" class="timer"></div>
                <button type="button" id="verifyBtn" class="verify-btn">
                    인증 확인
                </button>
            </div>
        </form>
    </div>

    <script>
        let countdownTimer;
        let timeLeft = 300; // 5분
        let isCodeSent = false;

        // 전화번호 포맷팅
        document.getElementById('phoneNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^0-9]/g, '');
            if (value.length >= 3) {
                if (value.length >= 7) {
                    value = value.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
                } else {
                    value = value.replace(/(\d{3})(\d{4})/, '$1-$2');
                }
            }
            e.target.value = value;
        });

        // 메시지 표시 함수
        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="message ${type}">${message}</div>`;
        }

        // 타이머 시작
        function startTimer() {
            const timerElement = document.getElementById('timer');
            countdownTimer = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerElement.textContent = `남은 시간: ${minutes}:${seconds.toString().padStart(2, '0')}`;
                
                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    timerElement.textContent = '인증 시간이 만료되었습니다.';
                    document.getElementById('verifyBtn').disabled = true;
                }
                timeLeft--;
            }, 1000);
        }

        // 인증번호 발송
        document.getElementById('sendCodeBtn').addEventListener('click', async function() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            const recaptchaResponse = grecaptcha.getResponse();

            if (!phoneNumber) {
                showMessage('전화번호를 입력해주세요.', 'error');
                return;
            }

            if (!recaptchaResponse) {
                showMessage('reCAPTCHA 인증을 완료해주세요.', 'error');
                return;
            }

            const phoneRegex = /^010-\d{4}-\d{4}$/;
            if (!phoneRegex.test(phoneNumber)) {
                showMessage('올바른 전화번호 형식으로 입력해주세요. (010-1234-5678)', 'error');
                return;
            }

            this.disabled = true;
            this.innerHTML = '<span class="loading"></span>발송 중...';

            try {
                // Firebase Functions 호출
                const response = await fetch('https://us-central1-parabara-1a504.cloudfunctions.net/sendSmsVerification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phoneNumber: phoneNumber,
                        recaptchaToken: recaptchaResponse
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('인증번호가 발송되었습니다.', 'success');
                    document.getElementById('verificationGroup').classList.remove('hidden');
                    isCodeSent = true;
                    timeLeft = 300;
                    startTimer();
                } else {
                    throw new Error(result.message || '인증번호 발송에 실패했습니다.');
                }
            } catch (error) {
                console.error('SMS 발송 오류:', error);
                showMessage(error.message || '인증번호 발송에 실패했습니다.', 'error');
            } finally {
                this.disabled = false;
                this.innerHTML = '재발송';
                grecaptcha.reset(); // reCAPTCHA 리셋
            }
        });

        // 인증 확인
        document.getElementById('verifyBtn').addEventListener('click', async function() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            const verificationCode = document.getElementById('verificationCode').value;

            if (!verificationCode) {
                showMessage('인증번호를 입력해주세요.', 'error');
                return;
            }

            if (verificationCode.length !== 6) {
                showMessage('6자리 인증번호를 입력해주세요.', 'error');
                return;
            }

            this.disabled = true;
            this.innerHTML = '<span class="loading"></span>인증 중...';

            try {
                // Firebase Functions 호출
                const response = await fetch('https://us-central1-parabara-1a504.cloudfunctions.net/verifySmsCode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phoneNumber: phoneNumber,
                        verificationCode: verificationCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('전화번호 인증이 완료되었습니다!', 'success');
                    clearInterval(countdownTimer);
                    
                    // Flutter 앱으로 결과 전달
                    if (window.flutter_inappwebview) {
                        window.flutter_inappwebview.callHandler('phoneVerificationSuccess', {
                            phoneNumber: phoneNumber,
                            verified: true
                        });
                    }
                    
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                } else {
                    throw new Error(result.message || '인증에 실패했습니다.');
                }
            } catch (error) {
                console.error('인증 확인 오류:', error);
                showMessage(error.message || '인증에 실패했습니다.', 'error');
            } finally {
                this.disabled = false;
                this.innerHTML = '인증 확인';
            }
        });

        // 인증번호 입력 시 자동 포맷팅
        document.getElementById('verificationCode').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    </script>
</body>
</html>
