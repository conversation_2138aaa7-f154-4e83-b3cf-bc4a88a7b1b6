{"Generated": "2025-08-16T06:18:35", "Root": "lib", "TotalCandidates": 4769, "UnusedCount": 756, "Items": [{"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 72, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 569, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\nickname.freezed.dart", "Line": 271, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 26, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 71, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.freezed.dart", "Line": 68, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 25, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.freezed.dart", "Line": 68, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 28, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 74, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\nickname.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 310, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.freezed.dart", "Line": 285, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 29, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 364, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 318, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 294, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 72, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 400, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 356, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 318, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.freezed.dart", "Line": 68, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 641, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.freezed.dart", "Line": 275, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 75, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 685, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.freezed.dart", "Line": 68, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 305, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 75, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 31, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.freezed.dart", "Line": 273, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 26, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 303, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\nickname.freezed.dart", "Line": 68, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.freezed.dart", "Line": 68, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.freezed.dart", "Line": 293, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.freezed.dart", "Line": 275, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sale_stat.freezed.dart", "Line": 273, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sale.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 71, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 299, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 25, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 295, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 67, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sale_stat.freezed.dart", "Line": 68, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sale_stat.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.freezed.dart", "Line": 309, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.freezed.dart", "Line": 273, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.freezed.dart", "Line": 70, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.freezed.dart", "Line": 24, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 23, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 302, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 612, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.freezed.dart", "Line": 271, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 330, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 376, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.freezed.dart", "Line": 69, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 903, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.freezed.dart", "Line": 23, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 601, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.freezed.dart", "Line": 65, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sale.freezed.dart", "Line": 281, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.freezed.dart", "Line": 293, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 71, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\sale.freezed.dart", "Line": 68, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.freezed.dart", "Line": 69, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 277, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.freezed.dart", "Line": 23, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "a", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 25, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\sale.freezed.dart", "Line": 216, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.freezed.dart", "Line": 212, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 227, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.freezed.dart", "Line": 212, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.freezed.dart", "Line": 213, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.freezed.dart", "Line": 227, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sale_stat.freezed.dart", "Line": 212, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 229, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.freezed.dart", "Line": 228, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 214, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 520, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.freezed.dart", "Line": 228, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 222, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.freezed.dart", "Line": 213, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 227, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 234, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 508, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 219, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\nickname.freezed.dart", "Line": 211, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "e", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.freezed.dart", "Line": 218, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.dart", "Line": 12, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.dart", "Line": 42, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.dart", "Line": 11, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.dart", "Line": 45, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 19, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.dart", "Line": 23, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 17, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 18, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 20, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 24, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 25, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.dart", "Line": 17, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.dart", "Line": 18, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.dart", "Line": 28, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.dart", "Line": 46, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.dart", "Line": 43, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.dart", "Line": 101, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.dart", "Line": 96, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.dart", "Line": 87, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.dart", "Line": 84, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.dart", "Line": 81, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.dart", "Line": 69, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.dart", "Line": 16, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.dart", "Line": 15, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.dart", "Line": 13, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.dart", "Line": 12, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.dart", "Line": 27, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.dart", "Line": 26, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.dart", "Line": 24, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.dart", "Line": 98, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.dart", "Line": 63, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.dart", "Line": 44, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 36, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.dart", "Line": 26, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.dart", "Line": 31, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.dart", "Line": 30, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 33, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 298, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.dart", "Line": 24, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 289, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 286, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.dart", "Line": 29, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 295, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 283, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.dart", "Line": 33, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 277, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 45, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 18, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 42, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.dart", "Line": 12, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 27, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 280, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "t", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.dart", "Line": 292, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 521, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 330, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 535, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 317, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 260, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 38, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 25, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 273, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 269, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.freezed.dart", "Line": 241, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 267, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 263, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 261, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 257, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.freezed.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 253, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 246, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.freezed.dart", "Line": 225, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\seller.freezed.dart", "Line": 239, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 30, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.freezed.dart", "Line": 246, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.freezed.dart", "Line": 227, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 27, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 40, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.freezed.dart", "Line": 35, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 553, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 567, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.freezed.dart", "Line": 215, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.freezed.dart", "Line": 221, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.freezed.dart", "Line": 245, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 24, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.freezed.dart", "Line": 259, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.freezed.dart", "Line": 245, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 223, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 229, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 243, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_item.freezed.dart", "Line": 35, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\user_settings.freezed.dart", "Line": 37, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 342, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\checklist_template.freezed.dart", "Line": 259, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 329, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 237, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 241, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 248, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 254, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 262, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount.freezed.dart", "Line": 276, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 247, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.freezed.dart", "Line": 20, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 28, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 261, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 41, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 231, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 233, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 254, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sync_metadata.freezed.dart", "Line": 268, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.freezed.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 355, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.freezed.dart", "Line": 226, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 567, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\nickname.freezed.dart", "Line": 223, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\nickname.freezed.dart", "Line": 237, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 271, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 257, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 250, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 37, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.freezed.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product.freezed.dart", "Line": 24, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.freezed.dart", "Line": 227, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 240, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_stat_item.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 37, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\nickname.freezed.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 254, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 237, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 230, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 223, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.freezed.dart", "Line": 24, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 22, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sale_stat.freezed.dart", "Line": 239, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sale_stat.freezed.dart", "Line": 225, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sale_stat.freezed.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sale_stat.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.freezed.dart", "Line": 275, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.freezed.dart", "Line": 261, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.freezed.dart", "Line": 36, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment.freezed.dart", "Line": 23, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.freezed.dart", "Line": 244, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_product_link.freezed.dart", "Line": 241, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.freezed.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 269, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 255, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 251, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 249, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 245, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 842, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\nickname.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 844, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 38, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 846, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 640, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 848, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 850, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 852, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 854, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sales_log.freezed.dart", "Line": 25, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 840, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.freezed.dart", "Line": 239, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\purchased_product.freezed.dart", "Line": 225, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sale.freezed.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sale.freezed.dart", "Line": 233, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.freezed.dart", "Line": 34, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sale.freezed.dart", "Line": 21, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\sale.freezed.dart", "Line": 247, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.freezed.dart", "Line": 251, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_state.freezed.dart", "Line": 858, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "y", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.freezed.dart", "Line": 237, "Private": false, "RefCount": 0, "Unused": true}, {"Name": "_addEvent", "File": "E:\\AppProjects\\parabara\\lib\\screens\\event\\event_list_screen.dart", "Line": 477, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_addProductItem", "File": "E:\\AppProjects\\parabara\\lib\\screens\\prepayment\\register_prepayment_screen.dart", "Line": 173, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_addSetDiscountToMemory", "File": "E:\\AppProjects\\parabara\\lib\\screens\\set_discount\\set_discount_dialog.dart", "Line": 87, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_cancelEditTemplate", "File": "E:\\AppProjects\\parabara\\lib\\screens\\checklist\\checklist_edit_dialog.dart", "Line": 127, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_completeOnboarding", "File": "E:\\AppProjects\\parabara\\lib\\main.dart", "Line": 368, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_createEvent", "File": "E:\\AppProjects\\parabara\\lib\\screens\\onboarding\\event_workspace_onboarding_screen.dart", "Line": 500, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_deleteAccount", "File": "E:\\AppProjects\\parabara\\lib\\screens\\settings\\my_page_screen.dart", "Line": 386, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_deleteSelectedProducts", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 865, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_handleAppleSignIn", "File": "E:\\AppProjects\\parabara\\lib\\screens\\auth\\login_screen.dart", "Line": 263, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_handleBatchError", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sales_log\\sales_log_batch_operations.dart", "Line": 49, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_handleForgotPassword", "File": "E:\\AppProjects\\parabara\\lib\\screens\\auth\\login_screen.dart", "Line": 189, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_handleGoogleSignIn", "File": "E:\\AppProjects\\parabara\\lib\\screens\\auth\\login_screen.dart", "Line": 216, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_handleResendEmail", "File": "E:\\AppProjects\\parabara\\lib\\screens\\auth\\login_screen.dart", "Line": 161, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_handleSave", "File": "E:\\AppProjects\\parabara\\lib\\screens\\auth\\nickname_screen.dart", "Line": 530, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_handleSubmit", "File": "E:\\AppProjects\\parabara\\lib\\screens\\set_discount\\set_discount_form_dialog.dart", "Line": 354, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_logout", "File": "E:\\AppProjects\\parabara\\lib\\screens\\settings\\my_page_screen.dart", "Line": 313, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_navigateToAddSellerScreen", "File": "E:\\AppProjects\\parabara\\lib\\screens\\seller\\seller_management_screen.dart", "Line": 96, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_navigateToSettings", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sales_log\\sales_log_screen.dart", "Line": 657, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_navigateToStatistics", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sales_log\\sales_log_screen.dart", "Line": 647, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onAuthStateChanged", "File": "E:\\AppProjects\\parabara\\lib\\services\\realtime_sync_service_main.dart", "Line": 749, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onConnectionChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\realtime_sync_provider.dart", "Line": 72, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onConnectivityChanged", "File": "E:\\AppProjects\\parabara\\lib\\services\\realtime_sync_service_main.dart", "Line": 734, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onCreate", "File": "E:\\AppProjects\\parabara\\lib\\services\\database_service.dart", "Line": 203, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onNicknameChanged", "File": "E:\\AppProjects\\parabara\\lib\\screens\\auth\\nickname_screen.dart", "Line": 91, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onProductDecrease", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 569, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onProductEditTap", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 588, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onProductLongPress", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 554, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onProductRemove", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 627, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onProductTap", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 535, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onQuantitiesChanged", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 2137, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onSaleLongPress", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 1015, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onSaleTap", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 1006, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onSearchTextChanged", "File": "E:\\AppProjects\\parabara\\lib\\screens\\inventory\\inventory_screen.dart", "Line": 218, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onSellWithMethod", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 1096, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onServiceDecrease", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 608, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onServiceRemove", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 639, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onServiceTap", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 593, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onSplashComplete", "File": "E:\\AppProjects\\parabara\\lib\\main.dart", "Line": 228, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onSubscriptionsChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\realtime_sync_provider.dart", "Line": 79, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_onUpgrade", "File": "E:\\AppProjects\\parabara\\lib\\services\\database_service.dart", "Line": 465, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_openQrScanScreen", "File": "E:\\AppProjects\\parabara\\lib\\screens\\inventory\\inventory_screen.dart", "Line": 753, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_pickAndUploadProfileImage", "File": "E:\\AppProjects\\parabara\\lib\\screens\\settings\\my_page_screen.dart", "Line": 79, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_pickExcelFile", "File": "E:\\AppProjects\\parabara\\lib\\screens\\excel\\excel_import_screen.dart", "Line": 657, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_recropImage", "File": "E:\\AppProjects\\parabara\\lib\\screens\\product\\register_product_screen.dart", "Line": 202, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_refreshOrderData", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 1632, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_registerProducts", "File": "E:\\AppProjects\\parabara\\lib\\screens\\product\\manual_bulk_product_registration_screen.dart", "Line": 748, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_reorderCategories", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 2646, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_reorderTemplates", "File": "E:\\AppProjects\\parabara\\lib\\screens\\checklist\\checklist_edit_dialog.dart", "Line": 135, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_saveAllChanges", "File": "E:\\AppProjects\\parabara\\lib\\screens\\revenue_goal\\revenue_goal_dialog.dart", "Line": 627, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_saveEvent", "File": "E:\\AppProjects\\parabara\\lib\\screens\\event\\event_form_screen.dart", "Line": 582, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_savePrepayment", "File": "E:\\AppProjects\\parabara\\lib\\screens\\prepayment\\register_prepayment_screen.dart", "Line": 205, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_saveProduct", "File": "E:\\AppProjects\\parabara\\lib\\screens\\product\\register_product_screen.dart", "Line": 261, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_shareFile", "File": "E:\\AppProjects\\parabara\\lib\\widgets\\pdf_preview_dialog.dart", "Line": 191, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showAddCategoryDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 2473, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showAddDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\set_discount\\set_discount_dialog.dart", "Line": 508, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showAddProductDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\product\\manual_bulk_product_registration_screen.dart", "Line": 191, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showChangePasswordDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\settings\\my_page_screen.dart", "Line": 288, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showFilterDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\records_and_statistics\\records_and_statistics_screen.dart", "Line": 279, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showModeSelector", "File": "E:\\AppProjects\\parabara\\lib\\screens\\excel\\excel_import_screen.dart", "Line": 484, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showPrepaymentSortDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\inventory\\inventory_screen.dart", "Line": 528, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showProductSelectionDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\prepayment\\register_prepayment_screen.dart", "Line": 160, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showQuantityInputDialog", "File": "E:\\AppProjects\\parabara\\lib\\widgets\\slider_quantity_widget.dart", "Line": 93, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showQuickInputDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\product\\manual_bulk_product_registration_screen.dart", "Line": 575, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showSortAndFilterDialog", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 1650, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showSortOptions", "File": "E:\\AppProjects\\parabara\\lib\\screens\\event\\event_list_screen.dart", "Line": 516, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_showTimeBasedSettings", "File": "E:\\AppProjects\\parabara\\lib\\screens\\records_and_statistics\\statistics_tab_content.dart", "Line": 2544, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_startRealtimeSync", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sync\\sync_confirmation_screen.dart", "Line": 264, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_switchAuthMode", "File": "E:\\AppProjects\\parabara\\lib\\main.dart", "Line": 451, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_toggleDeletionMode", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_screen.dart", "Line": 855, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_toggleSearchMode", "File": "E:\\AppProjects\\parabara\\lib\\screens\\inventory\\inventory_screen.dart", "Line": 241, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_updateDataLoadingState", "File": "E:\\AppProjects\\parabara\\lib\\providers\\unified_workspace_provider.dart", "Line": 307, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "_updateSetDiscountInMemory", "File": "E:\\AppProjects\\parabara\\lib\\screens\\set_discount\\set_discount_dialog.dart", "Line": 99, "Private": true, "RefCount": 1, "Unused": true}, {"Name": "addLink", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_product_link_provider.dart", "Line": 117, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "addSale", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sale_provider.dart", "Line": 57, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "addTask", "File": "E:\\AppProjects\\parabara\\lib\\utils\\offline_task.dart", "Line": 163, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "addTempPrepaymentData", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 162, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "amount", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 53, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "amountAsc", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 6, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "amountDesc", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 7, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "analyzeQuery", "File": "E:\\AppProjects\\parabara\\lib\\utils\\database_optimizer.dart", "Line": 308, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "AppError", "File": "E:\\AppProjects\\parabara\\lib\\utils\\provider_exception.dart", "Line": 7, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "assessSecurityRisk", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 790, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "average", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 184, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "BaseAsyncNotifier", "File": "E:\\AppProjects\\parabara\\lib\\providers\\base_async_notifier.dart", "Line": 19, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "batchDeleteProducts", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 876, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "batchInsertProducts", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 835, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "batchInsertSellers", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\seller_repository.dart", "Line": 202, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "batchUpdateSellers", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\seller_repository.dart", "Line": 222, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "buyerName", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 52, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "buyerNameAsc", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 4, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "buyerNameDesc", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 5, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "cacheData", "File": "E:\\AppProjects\\parabara\\lib\\utils\\memory_manager.dart", "Line": 445, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "calculateDelay", "File": "E:\\AppProjects\\parabara\\lib\\providers\\retry_policy.dart", "Line": 50, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "calculatePercentage", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 237, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "callback", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 174, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "cancelCurrentOperation", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sales_log_provider.dart", "Line": 721, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "cancelUpload", "File": "E:\\AppProjects\\parabara\\lib\\utils\\firebase_upload_utils.dart", "Line": 143, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "checkMemoryUsage", "File": "E:\\AppProjects\\parabara\\lib\\utils\\memory_manager.dart", "Line": 693, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "cleanup", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 906, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "cleanupExpiredCache", "File": "E:\\AppProjects\\parabara\\lib\\utils\\image_cache.dart", "Line": 430, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "clearAllSyncMetadata", "File": "E:\\AppProjects\\parabara\\lib\\services\\sync_metadata_service.dart", "Line": 201, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "clearEventCache", "File": "E:\\AppProjects\\parabara\\lib\\utils\\image_cache.dart", "Line": 223, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "clearEventSyncMetadata", "File": "E:\\AppProjects\\parabara\\lib\\services\\sync_metadata_service.dart", "Line": 183, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "clearOfflineTasks", "File": "E:\\AppProjects\\parabara\\lib\\services\\database_service.dart", "Line": 722, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "clearPriceFilter", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 346, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "clearSettings", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 39, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "clearTempPrepaymentData", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 167, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "clearUserSpecificData", "File": "E:\\AppProjects\\parabara\\lib\\utils\\local_data_cleaner.dart", "Line": 367, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "createBatchValuesClause", "File": "E:\\AppProjects\\parabara\\lib\\utils\\sql_utils.dart", "Line": 104, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "createSafeWhereIn", "File": "E:\\AppProjects\\parabara\\lib\\utils\\sql_utils.dart", "Line": 80, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "createTable", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\prepayment_product_link_repository.dart", "Line": 14, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "createTimeoutToken", "File": "E:\\AppProjects\\parabara\\lib\\utils\\cancellation_token.dart", "Line": 288, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "createToken", "File": "E:\\AppProjects\\parabara\\lib\\utils\\cancellation_token.dart", "Line": 281, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "critical", "File": "E:\\AppProjects\\parabara\\lib\\utils\\offline_task.dart", "Line": 9, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "darkenColor", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 209, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "dateTimeToTimestamp", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 40, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "daysBetween", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 227, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "debounce", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 155, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "defaultOption", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 94, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteAllByEventId", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\set_discount_transaction_repository.dart", "Line": 164, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteAllPrepayments", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\prepayment_repository.dart", "Line": 310, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteAllProducts", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 686, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteAllSellers", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\seller_repository.dart", "Line": 194, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteLog", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sales_log_crud.dart", "Line": 258, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deletePrepaymentVirtualProduct", "File": "E:\\AppProjects\\parabara\\lib\\services\\realtime_sync_service_main.dart", "Line": 1595, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteProductWithSalesLogs", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_notifier.dart", "Line": 630, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteSale", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sale_repository.dart", "Line": 74, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteSetting", "File": "E:\\AppProjects\\parabara\\lib\\providers\\settings_provider.dart", "Line": 445, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "deleteSinglePrepayment", "File": "E:\\AppProjects\\parabara\\lib\\services\\data_sync_service.dart", "Line": 795, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "discardChanges", "File": "E:\\AppProjects\\parabara\\lib\\providers\\checklist_provider.dart", "Line": 494, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "discount", "File": "E:\\AppProjects\\parabara\\lib\\models\\transaction_type.dart", "Line": 5, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "dismiss", "File": "E:\\AppProjects\\parabara\\lib\\utils\\toast_utils.dart", "Line": 123, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "disposeAction", "File": "E:\\AppProjects\\parabara\\lib\\utils\\local_data_cleaner.dart", "Line": 358, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "endDateAscending", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 24, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "endDateDescending", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 27, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "eventIdToWorkspaceId", "File": "E:\\AppProjects\\parabara\\lib\\utils\\event_workspace_utils.dart", "Line": 24, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "eventNameToWorkspaceName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\event_workspace_utils.dart", "Line": 34, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "EventSortOption", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 29, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "extractProducts", "File": "E:\\AppProjects\\parabara\\lib\\utils\\excel_processor.dart", "Line": 341, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "fileExists", "File": "E:\\AppProjects\\parabara\\lib\\utils\\firebase_upload_utils.dart", "Line": 185, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "forceRecoverNickname", "File": "E:\\AppProjects\\parabara\\lib\\providers\\nickname_provider.dart", "Line": 241, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "forceStopAllSync", "File": "E:\\AppProjects\\parabara\\lib\\providers\\global_sync_state_provider.dart", "Line": 226, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatAmPm", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 254, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatCurrencyCompact", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 63, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatCurrencyWithSign", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 225, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatCurrencyWithSymbol", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 18, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatDateTime", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 55, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatDotDateTime", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 264, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "FormatException", "File": "E:\\AppProjects\\parabara\\lib\\models\\event.dart", "Line": 65, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatFileSize", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 226, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatFullDisplay", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 70, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatKorDate", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 274, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatKorTime", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 279, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatMonthPeriod", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 289, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatNumber", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 249, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatPeriod", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 284, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatPrice", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 101, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatSimpleAmount", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 232, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatTimestampToDisplayDate", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 75, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatTimestampToDisplayDateTime", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 80, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatUtcToKst", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 331, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "formatWon", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 58, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "fromException", "File": "E:\\AppProjects\\parabara\\lib\\utils\\error_utils.dart", "Line": 129, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "fromFirebaseJson", "File": "E:\\AppProjects\\parabara\\lib\\models\\set_discount_transaction.dart", "Line": 101, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "fromOption", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 134, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "fromOrder", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 78, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "fullSync", "File": "E:\\AppProjects\\parabara\\lib\\providers\\fallback_data_provider.dart", "Line": 241, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "generateRandomString", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 138, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getActiveProducts", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 692, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getActiveTimers", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 516, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getAllByEventId", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\set_discount_transaction_repository.dart", "Line": 68, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getAllLinks", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\prepayment_product_link_repository.dart", "Line": 87, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getAllProducts", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 87, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getAllSellerNames", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 284, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getAmountColorHex", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 203, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getAnimationDuration", "File": "E:\\AppProjects\\parabara\\lib\\utils\\responsive_helper.dart", "Line": 226, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getByBatchSaleId", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\set_discount_transaction_repository.dart", "Line": 48, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCardCornerRadius", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_ui_components.dart", "Line": 39, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCardElevation", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_ui_components.dart", "Line": 32, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCardWidth", "File": "E:\\AppProjects\\parabara\\lib\\utils\\dimens.dart", "Line": 219, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCategoryColorByIndex", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 254, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCategoryColorFromValue", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 273, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCategoryColorIndex", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 262, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCategoryColorValue", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 278, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCollectDayOfWeekFromExcel", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 253, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getColorScheme", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 283, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getColumnCount", "File": "E:\\AppProjects\\parabara\\lib\\utils\\responsive_helper.dart", "Line": 204, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCrossAxisCount", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_ui_components.dart", "Line": 20, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCurrentEventData", "File": "E:\\AppProjects\\parabara\\lib\\services\\realtime_sync_service_main.dart", "Line": 259, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getCurrentTimestamp", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 30, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getDatabaseInfo", "File": "E:\\AppProjects\\parabara\\lib\\utils\\database_optimizer.dart", "Line": 268, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getDayOfWeekName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 103, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getDayOfWeekShortName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 125, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getDefaultSeller", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\seller_repository.dart", "Line": 245, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getDiscountedProductIds", "File": "E:\\AppProjects\\parabara\\lib\\services\\set_discount_service.dart", "Line": 303, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getEndedEvents", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\event_repository.dart", "Line": 328, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getEndOfDay", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 200, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getEndOfMonth", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 222, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getEndOfWeek", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 211, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getError", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validation_result.dart", "Line": 16, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getEventCount", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\event_repository.dart", "Line": 299, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getEventDayOfWeek", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 24, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getEventSyncMetadata", "File": "E:\\AppProjects\\parabara\\lib\\services\\sync_metadata_service.dart", "Line": 219, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getExcelDayOfWeekColumnIndex", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 291, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getFilterStatusText", "File": "E:\\AppProjects\\parabara\\lib\\screens\\statistics\\statistics_controller.dart", "Line": 262, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getFixedCardExtent", "File": "E:\\AppProjects\\parabara\\lib\\utils\\device_utils.dart", "Line": 427, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getGoal", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\revenue_goal_repository.dart", "Line": 61, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getGridColumns", "File": "E:\\AppProjects\\parabara\\lib\\utils\\dimens.dart", "Line": 212, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getHoverColor", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 240, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getInventoryColumns", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 369, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getInventoryColumnsLandscape", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 483, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getInventoryColumnsPortrait", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 445, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getItemSpacing", "File": "E:\\AppProjects\\parabara\\lib\\utils\\responsive_text_utils.dart", "Line": 152, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getLinkPrepaymentToInventory", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 329, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getLogFiles", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 334, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getMaxNameLines", "File": "E:\\AppProjects\\parabara\\lib\\utils\\device_utils.dart", "Line": 376, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getMemoryHistory", "File": "E:\\AppProjects\\parabara\\lib\\utils\\memory_manager.dart", "Line": 665, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getMessagesBySeverity", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validation_result.dart", "Line": 39, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getMethodCallCounts", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 511, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getOngoingEvents", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\event_repository.dart", "Line": 316, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getOption", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 110, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getOrder", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 60, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getPerformanceStats", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 476, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getPoolStats", "File": "E:\\AppProjects\\parabara\\lib\\utils\\object_pool.dart", "Line": 719, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getPressedColor", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 245, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getPriceTextSize", "File": "E:\\AppProjects\\parabara\\lib\\utils\\device_utils.dart", "Line": 226, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getProductCountByEventId", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 157, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getProductNameHeight", "File": "E:\\AppProjects\\parabara\\lib\\utils\\responsive_text_utils.dart", "Line": 47, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getProductNameSingleLineHeight", "File": "E:\\AppProjects\\parabara\\lib\\utils\\responsive_text_utils.dart", "Line": 63, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getProductNameTextSize", "File": "E:\\AppProjects\\parabara\\lib\\utils\\device_utils.dart", "Line": 221, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getProductsBySeller", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 320, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getProductsInStock", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 713, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getProductsSorted", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 181, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getQuantityTextPadding", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_ui_components.dart", "Line": 55, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getQuantityTextSize", "File": "E:\\AppProjects\\parabara\\lib\\utils\\device_utils.dart", "Line": 231, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getRelatedSalesLogs", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_notifier.dart", "Line": 712, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getRelativeDateString", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 182, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getRelativeTimeString", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 298, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getResponsiveMargin", "File": "E:\\AppProjects\\parabara\\lib\\utils\\dimens.dart", "Line": 205, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSaleById", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sale_repository.dart", "Line": 108, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSaleColumns", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 407, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSaleColumnsLandscape", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 559, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSaleColumnsPortrait", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\settings_repository.dart", "Line": 521, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSaleProductNameHeight", "File": "E:\\AppProjects\\parabara\\lib\\utils\\responsive_text_utils.dart", "Line": 75, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSaleProductNameTextStyle", "File": "E:\\AppProjects\\parabara\\lib\\utils\\responsive_text_utils.dart", "Line": 114, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSalesStatsByProduct", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sales_log_repository.dart", "Line": 952, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSetDiscountsByProductId", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\set_discount_repository.dart", "Line": 205, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getStartOfDay", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 195, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getStartOfMonth", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 217, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getStartOfWeek", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 205, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getSyncInfo", "File": "E:\\AppProjects\\parabara\\lib\\providers\\global_sync_state_provider.dart", "Line": 239, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getTask", "File": "E:\\AppProjects\\parabara\\lib\\utils\\offline_task.dart", "Line": 175, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getTempPrepaymentList", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 172, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getTextColorForBackground", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 234, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getTextMargin", "File": "E:\\AppProjects\\parabara\\lib\\screens\\sale\\sale_ui_components.dart", "Line": 46, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getTimestampAgoString", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 249, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getTotalPrepaymentAmount", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\prepayment_repository.dart", "Line": 316, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getTransactionTypeName", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sales_log_provider.dart", "Line": 727, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getUnreceivedCount", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\prepayment_repository.dart", "Line": 325, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "getUpcomingEvents", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\event_repository.dart", "Line": 322, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "groupProductsByCategory", "File": "E:\\AppProjects\\parabara\\lib\\utils\\product_display_utils.dart", "Line": 95, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "hasError<PERSON>hanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_state.dart", "Line": 122, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "hasFilterChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_state.dart", "Line": 127, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "hasFilteredProductsChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_state.dart", "Line": 99, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "hasLoadingChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_state.dart", "Line": 117, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "hasProductsChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_state.dart", "Line": 90, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "hasSellerNamesChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_state.dart", "Line": 108, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "hasSortOptionChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_state.dart", "Line": 132, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "hasStateChanged", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_state.dart", "Line": 137, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "high", "File": "E:\\AppProjects\\parabara\\lib\\utils\\offline_task.dart", "Line": 8, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "incrementCategory", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 191, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "incrementError", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 195, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "incrementLevel", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 187, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "initializeCategories", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\category_repository.dart", "Line": 35, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "initializeDateFormatting", "File": "E:\\AppProjects\\parabara\\lib\\main.dart", "Line": 161, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "initializeOptimizations", "File": "E:\\AppProjects\\parabara\\lib\\utils\\mobile_performance_utils.dart", "Line": 27, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "insertSale", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sale_repository.dart", "Line": 23, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "invalidateState", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 68, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isAboveMinimum", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 159, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isAlphabetic", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 343, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isAlphanumeric", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 348, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isBelowMaximum", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 164, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isInRange", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 154, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isInteger", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 337, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isNegative", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 99, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isPositive", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 94, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isPositiveInt", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 375, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isProductInDiscount", "File": "E:\\AppProjects\\parabara\\lib\\services\\set_discount_service.dart", "Line": 341, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isStrongPassword", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 415, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isTabletByWidth", "File": "E:\\AppProjects\\parabara\\lib\\utils\\dimens.dart", "Line": 123, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isTemplateChecked", "File": "E:\\AppProjects\\parabara\\lib\\providers\\checklist_provider.dart", "Line": 502, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isTodayDayOfWeek", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 153, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 454, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidCategoryColor", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_colors.dart", "Line": 268, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidCategoryName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 431, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidCompanyName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 462, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidCreditCard", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 489, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidDayOfWeek", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 387, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidDiscountAmount", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 392, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidDiscountPercentage", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 397, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidIPv4", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 496, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidKoreanName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 409, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidMacAddress", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 509, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidPostalCode", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 321, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidProductListArray", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 554, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidSearch<PERSON>uery", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 403, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidSqlValue", "File": "E:\\AppProjects\\parabara\\lib\\utils\\sql_utils.dart", "Line": 92, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidString", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 116, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidTagName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 439, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidTelecomNumber", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 476, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isValidUrl", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 326, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "isZero", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 104, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "lightenColor", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 217, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "loadLinksByProduct", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_product_link_provider.dart", "Line": 169, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "loadLinksByVirtualProduct", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_product_link_provider.dart", "Line": 158, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "loadSales", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sale_provider.dart", "Line": 197, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "logSystemInfo", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 723, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "low", "File": "E:\\AppProjects\\parabara\\lib\\utils\\offline_task.dart", "Line": 6, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "main", "File": "E:\\AppProjects\\parabara\\lib\\main.dart", "Line": 98, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "File": "E:\\AppProjects\\parabara\\lib\\widgets\\loading_indicator.dart", "Line": 90, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "nameAsc", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 3, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "nameAscending", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 12, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "nameDesc", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 4, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "nameDescending", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 15, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "normal", "File": "E:\\AppProjects\\parabara\\lib\\utils\\offline_task.dart", "Line": 7, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "oldestCreated", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 9, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "onAcquired", "File": "E:\\AppProjects\\parabara\\lib\\utils\\object_pool.dart", "Line": 795, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "onAppBackground", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 131, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "onAppForeground", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 110, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "onMemoryPressure", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 147, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "onProgress", "File": "E:\\AppProjects\\parabara\\lib\\utils\\firebase_upload_utils.dart", "Line": 51, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "onReleased", "File": "E:\\AppProjects\\parabara\\lib\\utils\\object_pool.dart", "Line": 798, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "onResult", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validation_rules.dart", "Line": 63, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "onScreenTransition", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 325, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "parseCurrency", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 76, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "parseDate", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 85, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "parseDateTime", "File": "E:\\AppProjects\\parabara\\lib\\utils\\date_utils.dart", "Line": 94, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "pauseSync", "File": "E:\\AppProjects\\parabara\\lib\\providers\\global_sync_state_provider.dart", "Line": 203, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "PrepaymentFilter", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_state.dart", "Line": 11, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "prepaymentOnly", "File": "E:\\AppProjects\\parabara\\lib\\screens\\excel\\excel_import_screen.dart", "Line": 30, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "PrepaymentSortOrder", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 9, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "PrepaymentSortType", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 55, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "price", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 45, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "priceAsc", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 7, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "priceDesc", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 8, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "printDebugTokenInstructions", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_check_utils.dart", "Line": 25, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "printProductionSetupGuide", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_check_utils.dart", "Line": 73, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "processor", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sales_log_repository.dart", "Line": 689, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "processSale", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sale_provider.dart", "Line": 97, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "ProductSortType", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 49, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "quantity", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 44, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "quantityAsc", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 5, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "quantityDesc", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 6, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "rawDelete", "File": "E:\\AppProjects\\parabara\\lib\\services\\database_service.dart", "Line": 742, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "rawInsert", "File": "E:\\AppProjects\\parabara\\lib\\services\\database_service.dart", "Line": 729, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "rawUpdate", "File": "E:\\AppProjects\\parabara\\lib\\services\\database_service.dart", "Line": 735, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "received", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_state.dart", "Line": 8, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "recentlyCreated", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 6, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "refreshAppCheckToken", "File": "E:\\AppProjects\\parabara\\lib\\utils\\firebase_service_wrapper.dart", "Line": 66, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "registrationDate", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 51, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "RegistrationMode", "File": "E:\\AppProjects\\parabara\\lib\\screens\\excel\\excel_import_screen.dart", "Line": 32, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "removePool", "File": "E:\\AppProjects\\parabara\\lib\\utils\\object_pool.dart", "Line": 705, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "removeProduct", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sale_provider.dart", "Line": 44, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "removeSale", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sale_provider.dart", "Line": 63, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "removeTask", "File": "E:\\AppProjects\\parabara\\lib\\utils\\offline_task.dart", "Line": 167, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "removeToken", "File": "E:\\AppProjects\\parabara\\lib\\utils\\cancellation_token.dart", "Line": 322, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "removeVirtualProduct", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_virtual_product_provider.dart", "Line": 249, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "reorder", "File": "E:\\AppProjects\\parabara\\lib\\providers\\payment_methods_provider.dart", "Line": 181, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "reset", "File": "E:\\AppProjects\\parabara\\lib\\utils\\object_pool.dart", "Line": 792, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "resetFilter", "File": "E:\\AppProjects\\parabara\\lib\\providers\\event_provider.dart", "Line": 569, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "resetState", "File": "E:\\AppProjects\\parabara\\lib\\providers\\data_sync_provider.dart", "Line": 444, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "resize", "File": "E:\\AppProjects\\parabara\\lib\\utils\\object_pool.dart", "Line": 618, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "returnAllObjects", "File": "E:\\AppProjects\\parabara\\lib\\utils\\object_pool.dart", "Line": 642, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "roundToTenThousand", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 174, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "roundToThousand", "File": "E:\\AppProjects\\parabara\\lib\\utils\\currency_utils.dart", "Line": 169, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "runLinkFlow", "File": "E:\\AppProjects\\parabara\\lib\\screens\\excel\\excel_import_screen.dart", "Line": 1010, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "safeDivide", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 243, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "safeParseBool", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 45, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "safeParseDouble", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 26, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "safeParseInt", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 13, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "safeParseString", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 39, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "sale", "File": "E:\\AppProjects\\parabara\\lib\\models\\transaction_type.dart", "Line": 3, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "search", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 523, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "searchEventsByName", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\event_repository.dart", "Line": 310, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "searchSellers", "File": "E:\\AppProjects\\parabara\\lib\\providers\\seller_provider.dart", "Line": 454, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "searchSellersByName", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\seller_repository.dart", "Line": 182, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "searchVirtualProducts", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_virtual_product_provider.dart", "Line": 285, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "selectEvent", "File": "E:\\AppProjects\\parabara\\lib\\providers\\event_provider.dart", "Line": 539, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "seller", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 46, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "sellerAsc", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 9, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "sellerDesc", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 10, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "service", "File": "E:\\AppProjects\\parabara\\lib\\models\\transaction_type.dart", "Line": 4, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "setCurrentScreen", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 310, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "setDefaultSellerByEventId", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\seller_repository.dart", "Line": 314, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "setDiscount", "File": "E:\\AppProjects\\parabara\\lib\\models\\transaction_type.dart", "Line": 6, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "setOnline", "File": "E:\\AppProjects\\parabara\\lib\\utils\\network_status.dart", "Line": 76, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "set<PERSON><PERSON>eived<PERSON><PERSON><PERSON>", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 507, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "setSearch<PERSON>uery", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 292, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "setSortOrder", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 487, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "setupTokenListener", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_check_utils.dart", "Line": 62, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "shouldAllowNameWrap", "File": "E:\\AppProjects\\parabara\\lib\\utils\\device_utils.dart", "Line": 325, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "shouldLog", "File": "E:\\AppProjects\\parabara\\lib\\utils\\logger_utils.dart", "Line": 59, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "shouldRetryException", "File": "E:\\AppProjects\\parabara\\lib\\providers\\retry_policy.dart", "Line": 43, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "shouldUpdateState", "File": "E:\\AppProjects\\parabara\\lib\\providers\\base_async_notifier.dart", "Line": 190, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "shouldUseVerticalLayout", "File": "E:\\AppProjects\\parabara\\lib\\utils\\device_utils.dart", "Line": 320, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "sqfliteFfiInit", "File": "E:\\AppProjects\\parabara\\lib\\services\\database_service.dart", "Line": 151, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "startDateAscending", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 18, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "startDateDescending", "File": "E:\\AppProjects\\parabara\\lib\\models\\event_sort_option.dart", "Line": 21, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "startMonitoring", "File": "E:\\AppProjects\\parabara\\lib\\utils\\network_status.dart", "Line": 14, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "StateSyncEvent", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 250, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "subscribeToEvents", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 200, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterAddPrepayment", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 759, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterAddProduct", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_provider.dart", "Line": 127, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterAddSalesLog", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sales_log_provider.dart", "Line": 851, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterAddSeller", "File": "E:\\AppProjects\\parabara\\lib\\providers\\seller_provider.dart", "Line": 663, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterDeletePrepayment", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 789, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterDeleteProduct", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_provider.dart", "Line": 157, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterDeleteSalesLog", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sales_log_provider.dart", "Line": 885, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterDeleteSeller", "File": "E:\\AppProjects\\parabara\\lib\\providers\\seller_provider.dart", "Line": 693, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterUpdatePrepayment", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 774, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterUpdateProduct", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_provider.dart", "Line": 142, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterUpdateSalesLog", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sales_log_provider.dart", "Line": 870, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAfterUpdateSeller", "File": "E:\\AppProjects\\parabara\\lib\\providers\\seller_provider.dart", "Line": 678, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAllPrepaymentData", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 744, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAllProductDataAsync", "File": "E:\\AppProjects\\parabara\\lib\\providers\\product_provider.dart", "Line": 122, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAllSalesLogData", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sales_log_provider.dart", "Line": 836, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncAllSellerData", "File": "E:\\AppProjects\\parabara\\lib\\providers\\seller_provider.dart", "Line": 648, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "syncState", "File": "E:\\AppProjects\\parabara\\lib\\utils\\state_sync_manager.dart", "Line": 315, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "TaskPriority", "File": "E:\\AppProjects\\parabara\\lib\\utils\\offline_task.dart", "Line": 11, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "throttle", "File": "E:\\AppProjects\\parabara\\lib\\utils\\common_utils.dart", "Line": 170, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "toggle", "File": "E:\\AppProjects\\parabara\\lib\\models\\product_sort_option.dart", "Line": 70, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "toggleItemCheck", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\checklist_repository.dart", "Line": 207, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "toggleProductActive", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 623, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "toggleReceiveStatus", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_provider.dart", "Line": 478, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "toggleShowActiveOnly", "File": "E:\\AppProjects\\parabara\\lib\\providers\\set_discount_provider.dart", "Line": 298, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "toMapWithId", "File": "E:\\AppProjects\\parabara\\lib\\models\\category.dart", "Line": 77, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "trackReference", "File": "E:\\AppProjects\\parabara\\lib\\utils\\memory_manager.dart", "Line": 243, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "TransactionType", "File": "E:\\AppProjects\\parabara\\lib\\models\\transaction_type.dart", "Line": 8, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "unlinkFrom", "File": "E:\\AppProjects\\parabara\\lib\\utils\\cancellation_token.dart", "Line": 114, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "unreceived", "File": "E:\\AppProjects\\parabara\\lib\\providers\\prepayment_state.dart", "Line": 9, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "unsubscribeFromEvent", "File": "E:\\AppProjects\\parabara\\lib\\services\\realtime_sync_service_main.dart", "Line": 993, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "untrackReference", "File": "E:\\AppProjects\\parabara\\lib\\utils\\memory_manager.dart", "Line": 254, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateError", "File": "E:\\AppProjects\\parabara\\lib\\providers\\base_async_notifier.dart", "Line": 154, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateMemoryUsage", "File": "E:\\AppProjects\\parabara\\lib\\utils\\batch_processor.dart", "Line": 211, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateMultipleProducts", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 485, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateProductImage", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 655, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateProductQuantity", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sale_provider.dart", "Line": 73, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateProfileImageLocal", "File": "E:\\AppProjects\\parabara\\lib\\providers\\nickname_provider.dart", "Line": 374, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateSale", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sale_repository.dart", "Line": 49, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateSaleQuantity", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sale_provider.dart", "Line": 83, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateSalesLogProductName", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sales_log_repository.dart", "Line": 1059, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateSelectedDateRange", "File": "E:\\AppProjects\\parabara\\lib\\screens\\statistics\\statistics_controller.dart", "Line": 51, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateSelectedSeller", "File": "E:\\AppProjects\\parabara\\lib\\screens\\statistics\\statistics_controller.dart", "Line": 45, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateSellerNameForProduct", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sales_log_repository.dart", "Line": 995, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateSellerNamesForProducts", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\sales_log_repository.dart", "Line": 1026, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateStock", "File": "E:\\AppProjects\\parabara\\lib\\repositories\\product_repository.dart", "Line": 549, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "updateTotalAmount", "File": "E:\\AppProjects\\parabara\\lib\\providers\\sale_provider.dart", "Line": 93, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateAlphabetic", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 90, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateAlphanumeric", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 100, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateDate", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 251, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateEmailAsync", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validation_rules.dart", "Line": 14, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 269, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateNickname", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 284, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateNumeric", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 80, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validatePassword", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 156, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validatePasswordConfirm", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 187, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validatePhoneNumber", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 122, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validatePostalCode", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 134, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validatePrepaymentWithRules", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validation_rules.dart", "Line": 269, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateProductWithRules", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validation_rules.dart", "Line": 257, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateSaleWithRules", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validation_rules.dart", "Line": 263, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateSetDiscount", "File": "E:\\AppProjects\\parabara\\lib\\services\\set_discount_service.dart", "Line": 347, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "validateUrl", "File": "E:\\AppProjects\\parabara\\lib\\utils\\validators.dart", "Line": 144, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "verifyAppCheckConfiguration", "File": "E:\\AppProjects\\parabara\\lib\\utils\\app_check_utils.dart", "Line": 40, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "watchUserSettings", "File": "E:\\AppProjects\\parabara\\lib\\services\\user_settings_service.dart", "Line": 121, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "workspaceIdToEventId", "File": "E:\\AppProjects\\parabara\\lib\\utils\\event_workspace_utils.dart", "Line": 29, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "workspaceNameToEventName", "File": "E:\\AppProjects\\parabara\\lib\\utils\\event_workspace_utils.dart", "Line": 39, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "writtenDateAsc", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 3, "Private": false, "RefCount": 1, "Unused": true}, {"Name": "writtenDateDesc", "File": "E:\\AppProjects\\parabara\\lib\\models\\prepayment_sort_order.dart", "Line": 2, "Private": false, "RefCount": 1, "Unused": true}]}